<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rice-Scatter Fantasy Map Generator</title>
  </head>
  <body>
    <div id="app">
      <!-- Header -->
      <header class="bg-gray-800 text-white p-4 shadow-lg">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold">Rice-Scatter Map Generator</h1>
          <div class="flex gap-2">
            <button id="reroll-btn" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors">
              🎲 Reroll
            </button>
            <button id="export-png-btn" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded transition-colors">
              📷 PNG
            </button>
            <button id="export-svg-btn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded transition-colors">
              📄 SVG
            </button>
          </div>
        </div>
      </header>

      <!-- Controls Panel -->
      <div class="bg-gray-100 p-4 border-b shadow-sm">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-6xl mx-auto">
          <!-- Seed Control -->
          <div class="flex flex-col">
            <label for="seed-input" class="text-sm font-medium text-gray-700 mb-1">Seed</label>
            <input
              type="number"
              id="seed-input"
              class="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value="12345"
              min="1"
              max="999999"
            />
          </div>

          <!-- Landmass Type Control -->
          <div class="flex flex-col">
            <label for="landmass-type-select" class="text-sm font-medium text-gray-700 mb-1">Type</label>
            <select
              id="landmass-type-select"
              class="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="continent">Continent</option>
              <option value="island" selected>Island</option>
              <option value="archipelago">Archipelago</option>
              <option value="peninsula">Peninsula</option>
            </select>
          </div>

          <!-- Complexity Control -->
          <div class="flex flex-col">
            <label for="complexity-slider" class="text-sm font-medium text-gray-700 mb-1">
              Complexity: <span id="complexity-value">0.5</span>
            </label>
            <input
              type="range"
              id="complexity-slider"
              class="w-full"
              min="0.1"
              max="1.0"
              step="0.1"
              value="0.5"
            />
          </div>

          <!-- Smoothness Control -->
          <div class="flex flex-col">
            <label for="smoothness-slider" class="text-sm font-medium text-gray-700 mb-1">
              Smoothness: <span id="smoothness-value">1</span>
            </label>
            <input
              type="range"
              id="smoothness-slider"
              class="w-full"
              min="0"
              max="3"
              value="1"
            />
          </div>
        </div>
      </div>

      <!-- Canvas Container -->
      <div class="flex-1 relative overflow-hidden bg-blue-50">
        <canvas
          id="map-canvas"
          class="absolute inset-0 w-full h-full"
          style="background: linear-gradient(to bottom, #87CEEB 0%, #4682B4 100%);"
        ></canvas>

        <!-- Instructions overlay -->
        <div class="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white p-3 rounded text-sm">
          <div>🖱️ Drag to pan • 🖱️ Scroll to zoom</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
