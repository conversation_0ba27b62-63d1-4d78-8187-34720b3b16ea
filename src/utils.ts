import seedrandom from 'seedrandom';

export interface Point {
  x: number;
  y: number;
}

export class SeededRandom {
  private rng: seedrandom.PRNG;

  constructor(seed: string | number) {
    this.rng = seedrandom(seed.toString());
  }

  random(): number {
    return this.rng();
  }

  randomRange(min: number, max: number): number {
    return min + this.random() * (max - min);
  }

  randomInt(min: number, max: number): number {
    return Math.floor(this.randomRange(min, max + 1));
  }
}

/**
 * Generate random points within a circular area
 */
export function generateRandomPoints(
  count: number, 
  centerX: number, 
  centerY: number, 
  radius: number, 
  rng: SeededRandom
): Point[] {
  const points: Point[] = [];
  
  for (let i = 0; i < count; i++) {
    // Use polar coordinates for more natural distribution
    const angle = rng.random() * 2 * Math.PI;
    const r = Math.sqrt(rng.random()) * radius; // sqrt for uniform distribution
    
    const x = centerX + r * Math.cos(angle);
    const y = centerY + r * Math.sin(angle);
    
    points.push({ x, y });
  }
  
  return points;
}

/**
 * <PERSON><PERSON><PERSON>'s corner cutting algorithm for smoothing polylines
 */
export function chaikinSmooth(points: Point[], iterations: number = 1): Point[] {
  let smoothed = [...points];
  
  for (let iter = 0; iter < iterations; iter++) {
    const newPoints: Point[] = [];
    
    for (let i = 0; i < smoothed.length; i++) {
      const current = smoothed[i];
      const next = smoothed[(i + 1) % smoothed.length];
      
      // Create two new points: 1/4 and 3/4 along the edge
      const p1 = {
        x: current.x * 0.75 + next.x * 0.25,
        y: current.y * 0.75 + next.y * 0.25
      };
      
      const p2 = {
        x: current.x * 0.25 + next.x * 0.75,
        y: current.y * 0.25 + next.y * 0.75
      };
      
      newPoints.push(p1, p2);
    }
    
    smoothed = newPoints;
  }
  
  return smoothed;
}

/**
 * Add Perlin-like noise to points for organic coastlines
 */
export function addCoastalJitter(
  points: Point[], 
  intensity: number, 
  rng: SeededRandom
): Point[] {
  return points.map((point, i) => {
    // Calculate normal vector (perpendicular to the coastline)
    const prev = points[(i - 1 + points.length) % points.length];
    const next = points[(i + 1) % points.length];
    
    // Tangent vector
    const tangent = {
      x: next.x - prev.x,
      y: next.y - prev.y
    };
    
    // Normal vector (perpendicular to tangent)
    const normal = {
      x: -tangent.y,
      y: tangent.x
    };
    
    // Normalize
    const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y);
    if (length > 0) {
      normal.x /= length;
      normal.y /= length;
    }
    
    // Add random displacement along the normal
    const displacement = (rng.random() - 0.5) * intensity;
    
    return {
      x: point.x + normal.x * displacement,
      y: point.y + normal.y * displacement
    };
  });
}

/**
 * Calculate distance between two points
 */
export function distance(p1: Point, p2: Point): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Clamp a value between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Linear interpolation
 */
export function lerp(a: number, b: number, t: number): number {
  return a + (b - a) * t;
}

/**
 * Convert points to format expected by concaveman
 */
export function pointsToCoordinates(points: Point[]): number[][] {
  return points.map(p => [p.x, p.y]);
}

/**
 * Convert coordinates back to points
 */
export function coordinatesToPoints(coords: number[][]): Point[] {
  return coords.map(([x, y]) => ({ x, y }));
}
