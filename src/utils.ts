import seedrandom from 'seedrandom';

export interface Point {
  x: number;
  y: number;
}

export class SeededRandom {
  private rng: seedrandom.PRNG;

  constructor(seed: string | number) {
    this.rng = seedrandom(seed.toString());
  }

  random(): number {
    return this.rng();
  }

  randomRange(min: number, max: number): number {
    return min + this.random() * (max - min);
  }

  randomInt(min: number, max: number): number {
    return Math.floor(this.randomRange(min, max + 1));
  }
}

/**
 * Generate landmass using fractal noise and blob generation
 */
export function generateLandmass(
  width: number,
  height: number,
  rng: SeededRandom,
  complexity: number = 0.5
): Point[] {
  const centerX = width / 2;
  const centerY = height / 2;

  // Create base shape using multiple overlapping blobs
  const numBlobs = rng.randomInt(2, 6);
  const blobs: Array<{x: number, y: number, radius: number}> = [];

  for (let i = 0; i < numBlobs; i++) {
    const angle = (i / numBlobs) * 2 * Math.PI + rng.randomRange(-0.5, 0.5);
    const distance = rng.randomRange(0, Math.min(width, height) * 0.15);

    blobs.push({
      x: centerX + Math.cos(angle) * distance,
      y: centerY + Math.sin(angle) * distance,
      radius: rng.randomRange(Math.min(width, height) * 0.1, Math.min(width, height) * 0.25)
    });
  }

  // Generate coastline points using the blob influence
  const numPoints = Math.floor(50 + complexity * 100);
  const points: Point[] = [];

  for (let i = 0; i < numPoints; i++) {
    const angle = (i / numPoints) * 2 * Math.PI;

    // Calculate distance based on blob influence
    let maxInfluence = 0;
    for (const blob of blobs) {
      const blobAngle = Math.atan2(centerY - blob.y, centerX - blob.x);
      const angleDiff = Math.abs(angle - blobAngle);
      const normalizedDiff = Math.min(angleDiff, 2 * Math.PI - angleDiff);
      const influence = blob.radius * Math.exp(-normalizedDiff * 2);
      maxInfluence = Math.max(maxInfluence, influence);
    }

    // Add fractal noise for organic coastlines
    const noise1 = (Math.sin(angle * 3 + rng.random() * 10) + 1) / 2;
    const noise2 = (Math.sin(angle * 7 + rng.random() * 10) + 1) / 2;
    const noise3 = (Math.sin(angle * 13 + rng.random() * 10) + 1) / 2;

    const fractalNoise = (noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2);
    const noiseAmount = maxInfluence * 0.3 * complexity;

    const distance = maxInfluence + (fractalNoise - 0.5) * noiseAmount;

    const x = centerX + Math.cos(angle) * distance;
    const y = centerY + Math.sin(angle) * distance;

    points.push({ x, y });
  }

  return points;
}

/**
 * Generate archipelago (multiple islands)
 */
export function generateArchipelago(
  width: number,
  height: number,
  rng: SeededRandom,
  islandCount: number = 3
): Point[][] {
  const islands: Point[][] = [];
  const usedAreas: Array<{x: number, y: number, radius: number}> = [];

  for (let i = 0; i < islandCount; i++) {
    let attempts = 0;
    let validPosition = false;
    let islandX, islandY, islandSize;

    // Find a position that doesn't overlap with existing islands
    while (!validPosition && attempts < 20) {
      islandX = rng.randomRange(width * 0.2, width * 0.8);
      islandY = rng.randomRange(height * 0.2, height * 0.8);
      islandSize = rng.randomRange(Math.min(width, height) * 0.05, Math.min(width, height) * 0.15);

      validPosition = true;
      for (const area of usedAreas) {
        const distance = Math.sqrt((islandX - area.x) ** 2 + (islandY - area.y) ** 2);
        if (distance < area.radius + islandSize + 20) {
          validPosition = false;
          break;
        }
      }
      attempts++;
    }

    if (validPosition) {
      usedAreas.push({ x: islandX!, y: islandY!, radius: islandSize! });

      // Generate island shape
      const numPoints = rng.randomInt(12, 24);
      const island: Point[] = [];

      for (let j = 0; j < numPoints; j++) {
        const angle = (j / numPoints) * 2 * Math.PI;
        const radiusVariation = rng.randomRange(0.6, 1.4);
        const distance = islandSize! * radiusVariation;

        const x = islandX! + Math.cos(angle) * distance;
        const y = islandY! + Math.sin(angle) * distance;

        island.push({ x, y });
      }

      islands.push(island);
    }
  }

  return islands;
}

/**
 * Chaikin's corner cutting algorithm for smoothing polylines
 */
export function chaikinSmooth(points: Point[], iterations: number = 1): Point[] {
  let smoothed = [...points];
  
  for (let iter = 0; iter < iterations; iter++) {
    const newPoints: Point[] = [];
    
    for (let i = 0; i < smoothed.length; i++) {
      const current = smoothed[i];
      const next = smoothed[(i + 1) % smoothed.length];
      
      // Create two new points: 1/4 and 3/4 along the edge
      const p1 = {
        x: current.x * 0.75 + next.x * 0.25,
        y: current.y * 0.75 + next.y * 0.25
      };
      
      const p2 = {
        x: current.x * 0.25 + next.x * 0.75,
        y: current.y * 0.25 + next.y * 0.75
      };
      
      newPoints.push(p1, p2);
    }
    
    smoothed = newPoints;
  }
  
  return smoothed;
}

/**
 * Add Perlin-like noise to points for organic coastlines
 */
export function addCoastalJitter(
  points: Point[], 
  intensity: number, 
  rng: SeededRandom
): Point[] {
  return points.map((point, i) => {
    // Calculate normal vector (perpendicular to the coastline)
    const prev = points[(i - 1 + points.length) % points.length];
    const next = points[(i + 1) % points.length];
    
    // Tangent vector
    const tangent = {
      x: next.x - prev.x,
      y: next.y - prev.y
    };
    
    // Normal vector (perpendicular to tangent)
    const normal = {
      x: -tangent.y,
      y: tangent.x
    };
    
    // Normalize
    const length = Math.sqrt(normal.x * normal.x + normal.y * normal.y);
    if (length > 0) {
      normal.x /= length;
      normal.y /= length;
    }
    
    // Add random displacement along the normal
    const displacement = (rng.random() - 0.5) * intensity;
    
    return {
      x: point.x + normal.x * displacement,
      y: point.y + normal.y * displacement
    };
  });
}

/**
 * Calculate distance between two points
 */
export function distance(p1: Point, p2: Point): number {
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Clamp a value between min and max
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Linear interpolation
 */
export function lerp(a: number, b: number, t: number): number {
  return a + (b - a) * t;
}

/**
 * Convert points to format expected by concaveman
 */
export function pointsToCoordinates(points: Point[]): number[][] {
  return points.map(p => [p.x, p.y]);
}

/**
 * Convert coordinates back to points
 */
export function coordinatesToPoints(coords: number[][]): Point[] {
  return coords.map(([x, y]) => ({ x, y }));
}
