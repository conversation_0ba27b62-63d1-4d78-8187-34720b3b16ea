import type { Point } from './utils';
import {
  SeededRandom,
  generateLandmass,
  generateArchipelago,
  chaikinSmooth,
  addCoastalJitter
} from './utils';

export type LandmassType = 'continent' | 'island' | 'archipelago' | 'peninsula';

export interface MapGenerationParams {
  seed: number;
  landmassType: LandmassType;
  complexity: number; // 0-1, affects coastline detail
  smoothness: number; // 0-3, smoothing iterations
  width: number;
  height: number;
}

export interface GeneratedMap {
  landmasses: Point[][]; // Multiple landmasses for archipelagos
  boundingBox: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
}

export class MapGenerator {
  private rng: SeededRandom;
  private params: MapGenerationParams;

  constructor(params: MapGenerationParams) {
    this.params = params;
    this.rng = new SeededRandom(params.seed);
  }

  /**
   * Generate a complete fantasy map
   */
  generateMap(): GeneratedMap {
    let landmasses: Point[][] = [];

    switch (this.params.landmassType) {
      case 'continent':
        landmasses = [this.generateContinent()];
        break;
      case 'island':
        landmasses = [this.generateIsland()];
        break;
      case 'archipelago':
        landmasses = this.generateArchipelagoMap();
        break;
      case 'peninsula':
        landmasses = [this.generatePeninsula()];
        break;
    }

    // Apply smoothing and jitter to all landmasses
    landmasses = landmasses.map(landmass => {
      let smoothed = landmass;

      // Apply smoothing if requested
      if (this.params.smoothness > 0) {
        smoothed = chaikinSmooth(smoothed, this.params.smoothness);
      }

      // Add coastal jitter for organic appearance
      const jitterIntensity = 10 + this.params.complexity * 20;
      smoothed = addCoastalJitter(smoothed, jitterIntensity, this.rng);

      return smoothed;
    });

    // Calculate bounding box for all landmasses
    const boundingBox = this.calculateBoundingBoxForLandmasses(landmasses);

    return {
      landmasses,
      boundingBox
    };
  }

  private generateContinent(): Point[] {
    // Large, complex landmass that can span most of the map
    const width = this.params.width * 0.8;
    const height = this.params.height * 0.7;
    const offsetX = (this.params.width - width) / 2;
    const offsetY = (this.params.height - height) / 2;

    const points = generateLandmass(width, height, this.rng, this.params.complexity);

    // Offset to center
    return points.map(p => ({ x: p.x + offsetX, y: p.y + offsetY }));
  }

  private generateIsland(): Point[] {
    // Medium-sized island in the center
    const size = Math.min(this.params.width, this.params.height) * 0.4;
    const centerX = this.params.width / 2;
    const centerY = this.params.height / 2;

    const points = generateLandmass(size, size, this.rng, this.params.complexity);

    // Center the island
    const offsetX = centerX - size / 2;
    const offsetY = centerY - size / 2;

    return points.map(p => ({ x: p.x + offsetX, y: p.y + offsetY }));
  }

  private generateArchipelagoMap(): Point[][] {
    const islandCount = this.rng.randomInt(3, 7);
    return generateArchipelago(this.params.width, this.params.height, this.rng, islandCount);
  }

  private generatePeninsula(): Point[] {
    // Landmass that extends from one edge
    const edge = this.rng.randomInt(0, 4); // 0=top, 1=right, 2=bottom, 3=left
    const width = this.params.width * 0.6;
    const height = this.params.height * 0.6;

    let points = generateLandmass(width, height, this.rng, this.params.complexity);

    // Extend towards the chosen edge
    switch (edge) {
      case 0: // Top
        points = points.map(p => ({ x: p.x + this.params.width * 0.2, y: p.y }));
        break;
      case 1: // Right
        points = points.map(p => ({ x: p.x + this.params.width * 0.4, y: p.y + this.params.height * 0.2 }));
        break;
      case 2: // Bottom
        points = points.map(p => ({ x: p.x + this.params.width * 0.2, y: p.y + this.params.height * 0.4 }));
        break;
      case 3: // Left
        points = points.map(p => ({ x: p.x, y: p.y + this.params.height * 0.2 }));
        break;
    }

    return points;
  }

  /**
   * Calculate bounding box for multiple landmasses
   */
  private calculateBoundingBoxForLandmasses(landmasses: Point[][]): GeneratedMap['boundingBox'] {
    if (landmasses.length === 0 || landmasses.every(l => l.length === 0)) {
      return { minX: 0, maxX: 0, minY: 0, maxY: 0 };
    }

    let minX = Infinity;
    let maxX = -Infinity;
    let minY = Infinity;
    let maxY = -Infinity;

    for (const landmass of landmasses) {
      for (const point of landmass) {
        minX = Math.min(minX, point.x);
        maxX = Math.max(maxX, point.x);
        minY = Math.min(minY, point.y);
        maxY = Math.max(maxY, point.y);
      }
    }

    return { minX, maxX, minY, maxY };
  }

  /**
   * Update generation parameters and regenerate
   */
  updateParams(newParams: Partial<MapGenerationParams>): GeneratedMap {
    this.params = { ...this.params, ...newParams };
    
    // Only recreate RNG if seed changed
    if (newParams.seed !== undefined) {
      this.rng = new SeededRandom(newParams.seed);
    }

    return this.generateMap();
  }

  /**
   * Get current parameters
   */
  getParams(): MapGenerationParams {
    return { ...this.params };
  }
}
