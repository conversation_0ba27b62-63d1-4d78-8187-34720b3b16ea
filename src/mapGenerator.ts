import concaveman from 'concaveman';
import {
  Point,
  SeededRandom,
  generateRandomPoints,
  chaikinSmooth,
  addCoastalJitter,
  pointsToCoordinates,
  coordinatesToPoints
} from './utils';

export interface MapGenerationParams {
  seed: number;
  pointCount: number;
  concavity: number;
  smoothness: number;
  width: number;
  height: number;
}

export interface GeneratedMap {
  coastline: Point[];
  originalPoints: Point[];
  boundingBox: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
}

export class MapGenerator {
  private rng: SeededRandom;
  private params: MapGenerationParams;

  constructor(params: MapGenerationParams) {
    this.params = params;
    this.rng = new SeededRandom(params.seed);
  }

  /**
   * Generate a complete fantasy map
   */
  generateMap(): GeneratedMap {
    // Generate random points in a circular distribution
    const centerX = this.params.width / 2;
    const centerY = this.params.height / 2;
    const radius = Math.min(this.params.width, this.params.height) * 0.3;

    const originalPoints = generateRandomPoints(
      this.params.pointCount,
      centerX,
      centerY,
      radius,
      this.rng
    );

    // Convert to format expected by concaveman
    const coordinates = pointsToCoordinates(originalPoints);

    // Generate concave hull (coastline)
    let hullCoords: number[][];
    try {
      hullCoords = concaveman(coordinates, this.params.concavity);
    } catch (error) {
      console.warn('Concaveman failed, falling back to convex hull');
      // Fallback to a simple convex hull approach
      hullCoords = this.generateConvexHull(coordinates);
    }

    let coastline = coordinatesToPoints(hullCoords);

    // Apply smoothing if requested
    if (this.params.smoothness > 0) {
      coastline = chaikinSmooth(coastline, this.params.smoothness);
    }

    // Add coastal jitter for organic appearance
    const jitterIntensity = 15; // pixels
    coastline = addCoastalJitter(coastline, jitterIntensity, this.rng);

    // Calculate bounding box
    const boundingBox = this.calculateBoundingBox(coastline);

    return {
      coastline,
      originalPoints,
      boundingBox
    };
  }

  /**
   * Simple convex hull as fallback (Gift wrapping algorithm)
   */
  private generateConvexHull(points: number[][]): number[][] {
    if (points.length < 3) return points;

    // Find the leftmost point
    let leftmost = 0;
    for (let i = 1; i < points.length; i++) {
      if (points[i][0] < points[leftmost][0]) {
        leftmost = i;
      }
    }

    const hull: number[][] = [];
    let current = leftmost;

    do {
      hull.push(points[current]);
      let next = (current + 1) % points.length;

      for (let i = 0; i < points.length; i++) {
        if (this.orientation(points[current], points[i], points[next]) === 2) {
          next = i;
        }
      }

      current = next;
    } while (current !== leftmost);

    return hull;
  }

  /**
   * Calculate orientation of three points
   * 0 -> Collinear, 1 -> Clockwise, 2 -> Counterclockwise
   */
  private orientation(p: number[], q: number[], r: number[]): number {
    const val = (q[1] - p[1]) * (r[0] - q[0]) - (q[0] - p[0]) * (r[1] - q[1]);
    if (val === 0) return 0;
    return val > 0 ? 1 : 2;
  }

  /**
   * Calculate bounding box of points
   */
  private calculateBoundingBox(points: Point[]): GeneratedMap['boundingBox'] {
    if (points.length === 0) {
      return { minX: 0, maxX: 0, minY: 0, maxY: 0 };
    }

    let minX = points[0].x;
    let maxX = points[0].x;
    let minY = points[0].y;
    let maxY = points[0].y;

    for (const point of points) {
      minX = Math.min(minX, point.x);
      maxX = Math.max(maxX, point.x);
      minY = Math.min(minY, point.y);
      maxY = Math.max(maxY, point.y);
    }

    return { minX, maxX, minY, maxY };
  }

  /**
   * Update generation parameters and regenerate
   */
  updateParams(newParams: Partial<MapGenerationParams>): GeneratedMap {
    this.params = { ...this.params, ...newParams };
    
    // Only recreate RNG if seed changed
    if (newParams.seed !== undefined) {
      this.rng = new SeededRandom(newParams.seed);
    }

    return this.generateMap();
  }

  /**
   * Get current parameters
   */
  getParams(): MapGenerationParams {
    return { ...this.params };
  }
}
