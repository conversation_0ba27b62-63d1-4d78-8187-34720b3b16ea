import type { Point } from './utils';
import { clamp } from './utils';
import type { GeneratedMap } from './mapGenerator';

export interface ViewTransform {
  offsetX: number;
  offsetY: number;
  scale: number;
}

export class MapRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private transform: ViewTransform;
  private isDragging: boolean = false;
  private lastMousePos: { x: number; y: number } = { x: 0, y: 0 };
  private animationId: number | null = null;
  private currentMap: GeneratedMap | null = null;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get 2D context from canvas');
    }
    this.ctx = ctx;

    // Initialize transform
    this.transform = {
      offsetX: 0,
      offsetY: 0,
      scale: 1
    };

    this.setupEventListeners();
    this.setupCanvas();
    this.startRenderLoop();
  }

  private setupCanvas(): void {
    // Set up high DPI rendering
    const dpr = window.devicePixelRatio || 1;
    const rect = this.canvas.getBoundingClientRect();
    
    this.canvas.width = rect.width * dpr;
    this.canvas.height = rect.height * dpr;
    
    this.ctx.scale(dpr, dpr);
    this.canvas.style.width = rect.width + 'px';
    this.canvas.style.height = rect.height + 'px';

    // Center the view
    this.transform.offsetX = rect.width / 2;
    this.transform.offsetY = rect.height / 2;
  }

  private setupEventListeners(): void {
    // Mouse events for pan and zoom
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('mouseleave', this.onMouseUp.bind(this));
    this.canvas.addEventListener('wheel', this.onWheel.bind(this));

    // Touch events for mobile
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));

    // Handle canvas resize
    window.addEventListener('resize', this.onResize.bind(this));
  }

  private onMouseDown(event: MouseEvent): void {
    this.isDragging = true;
    this.lastMousePos = { x: event.clientX, y: event.clientY };
    this.canvas.style.cursor = 'grabbing';
  }

  private onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) return;

    const deltaX = event.clientX - this.lastMousePos.x;
    const deltaY = event.clientY - this.lastMousePos.y;

    this.transform.offsetX += deltaX;
    this.transform.offsetY += deltaY;

    this.lastMousePos = { x: event.clientX, y: event.clientY };
  }

  private onMouseUp(): void {
    this.isDragging = false;
    this.canvas.style.cursor = 'grab';
  }

  private onWheel(event: WheelEvent): void {
    event.preventDefault();

    const rect = this.canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newScale = clamp(this.transform.scale * scaleFactor, 0.1, 10);

    // Zoom towards mouse position
    const scaleRatio = newScale / this.transform.scale;
    this.transform.offsetX = mouseX - (mouseX - this.transform.offsetX) * scaleRatio;
    this.transform.offsetY = mouseY - (mouseY - this.transform.offsetY) * scaleRatio;
    this.transform.scale = newScale;
  }

  private onTouchStart(event: TouchEvent): void {
    event.preventDefault();
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.isDragging = true;
      this.lastMousePos = { x: touch.clientX, y: touch.clientY };
    }
  }

  private onTouchMove(event: TouchEvent): void {
    event.preventDefault();
    if (event.touches.length === 1 && this.isDragging) {
      const touch = event.touches[0];
      const deltaX = touch.clientX - this.lastMousePos.x;
      const deltaY = touch.clientY - this.lastMousePos.y;

      this.transform.offsetX += deltaX;
      this.transform.offsetY += deltaY;

      this.lastMousePos = { x: touch.clientX, y: touch.clientY };
    }
  }

  private onTouchEnd(): void {
    this.isDragging = false;
  }

  private onResize(): void {
    this.setupCanvas();
  }

  private startRenderLoop(): void {
    const render = () => {
      this.render();
      this.animationId = requestAnimationFrame(render);
    };
    render();
  }

  private render(): void {
    const rect = this.canvas.getBoundingClientRect();
    
    // Clear canvas
    this.ctx.clearRect(0, 0, rect.width, rect.height);

    if (!this.currentMap) return;

    // Save context state
    this.ctx.save();

    // Apply transform
    this.ctx.translate(this.transform.offsetX, this.transform.offsetY);
    this.ctx.scale(this.transform.scale, this.transform.scale);

    // Draw the map
    this.drawMap(this.currentMap);

    // Restore context state
    this.ctx.restore();
  }

  private drawMap(map: GeneratedMap): void {
    // Draw all landmasses
    for (const landmass of map.landmasses) {
      this.drawCoastline(landmass);
    }
  }

  private drawPoints(points: Point[], color: string, radius: number): void {
    this.ctx.fillStyle = color;
    for (const point of points) {
      this.ctx.beginPath();
      this.ctx.arc(point.x, point.y, radius, 0, 2 * Math.PI);
      this.ctx.fill();
    }
  }

  private drawCoastline(coastline: Point[]): void {
    if (coastline.length < 3) return;

    // Fill the landmass
    this.ctx.fillStyle = '#8FBC8F'; // Sea green for land
    this.ctx.beginPath();
    this.ctx.moveTo(coastline[0].x, coastline[0].y);
    
    for (let i = 1; i < coastline.length; i++) {
      this.ctx.lineTo(coastline[i].x, coastline[i].y);
    }
    
    this.ctx.closePath();
    this.ctx.fill();

    // Draw coastline outline
    this.ctx.strokeStyle = '#2F4F2F'; // Dark sea green
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
  }

  /**
   * Update the map to render
   */
  setMap(map: GeneratedMap): void {
    this.currentMap = map;
  }

  /**
   * Reset view to center on the map
   */
  resetView(): void {
    if (!this.currentMap) return;

    const rect = this.canvas.getBoundingClientRect();
    const bbox = this.currentMap.boundingBox;
    
    // Calculate scale to fit the map
    const mapWidth = bbox.maxX - bbox.minX;
    const mapHeight = bbox.maxY - bbox.minY;
    const scaleX = (rect.width * 0.8) / mapWidth;
    const scaleY = (rect.height * 0.8) / mapHeight;
    
    this.transform.scale = Math.min(scaleX, scaleY, 2); // Max scale of 2
    
    // Center the map
    const mapCenterX = (bbox.minX + bbox.maxX) / 2;
    const mapCenterY = (bbox.minY + bbox.maxY) / 2;
    
    this.transform.offsetX = rect.width / 2 - mapCenterX * this.transform.scale;
    this.transform.offsetY = rect.height / 2 - mapCenterY * this.transform.scale;
  }

  /**
   * Export canvas as PNG
   */
  exportPNG(): void {
    const link = document.createElement('a');
    link.download = `fantasy-map-${Date.now()}.png`;
    link.href = this.canvas.toDataURL();
    link.click();
  }

  /**
   * Export as SVG
   */
  exportSVG(): void {
    if (!this.currentMap) return;

    const bbox = this.currentMap.boundingBox;
    const width = bbox.maxX - bbox.minX + 100; // Add padding
    const height = bbox.maxY - bbox.minY + 100;

    let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
    svg += `<rect width="100%" height="100%" fill="#87CEEB"/>`;

    // Create paths for all landmasses
    for (const landmass of this.currentMap.landmasses) {
      if (landmass.length > 0) {
        const offsetX = 50 - bbox.minX;
        const offsetY = 50 - bbox.minY;

        let pathData = `M ${landmass[0].x + offsetX} ${landmass[0].y + offsetY}`;
        for (let i = 1; i < landmass.length; i++) {
          const point = landmass[i];
          pathData += ` L ${point.x + offsetX} ${point.y + offsetY}`;
        }
        pathData += ' Z';

        svg += `<path d="${pathData}" fill="#8FBC8F" stroke="#2F4F2F" stroke-width="2"/>`;
      }
    }

    svg += '</svg>';

    const blob = new Blob([svg], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.download = `fantasy-map-${Date.now()}.svg`;
    link.href = url;
    link.click();
    URL.revokeObjectURL(url);
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
  }
}
