@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the map generator */
body {
  margin: 0;
  font-family: system-ui, -apple-system, sans-serif;
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

canvas {
  cursor: grab;
}

canvas:active {
  cursor: grabbing;
}

/* Custom slider styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: #e5e7eb;
  height: 4px;
  border-radius: 2px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #3b82f6;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: none;
}

input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 4px;
  border-radius: 2px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: #3b82f6;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
}
