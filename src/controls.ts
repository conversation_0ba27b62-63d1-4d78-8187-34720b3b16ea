import { MapGenerationParams } from './mapGenerator';

export interface ControlsConfig {
  onParamsChange: (params: Partial<MapGenerationParams>) => void;
  onReroll: () => void;
  onExportPNG: () => void;
  onExportSVG: () => void;
}

export class Controls {
  private config: ControlsConfig;
  private elements: {
    seedInput: HTMLInputElement;
    pointCountSlider: HTMLInputElement;
    pointCountValue: HTMLSpanElement;
    concavitySlider: HTMLInputElement;
    concavityValue: HTMLSpanElement;
    smoothnessSlider: HTMLInputElement;
    smoothnessValue: HTMLSpanElement;
    rerollBtn: HTMLButtonElement;
    exportPngBtn: HTMLButtonElement;
    exportSvgBtn: HTMLButtonElement;
  };

  constructor(config: ControlsConfig) {
    this.config = config;
    this.elements = this.getElements();
    this.setupEventListeners();
    this.updateDisplayValues();
  }

  private getElements() {
    const getElementById = <T extends HTMLElement>(id: string): T => {
      const element = document.getElementById(id) as T;
      if (!element) {
        throw new Error(`Element with id '${id}' not found`);
      }
      return element;
    };

    return {
      seedInput: getElementById<HTMLInputElement>('seed-input'),
      pointCountSlider: getElementById<HTMLInputElement>('point-count-slider'),
      pointCountValue: getElementById<HTMLSpanElement>('point-count-value'),
      concavitySlider: getElementById<HTMLInputElement>('concavity-slider'),
      concavityValue: getElementById<HTMLSpanElement>('concavity-value'),
      smoothnessSlider: getElementById<HTMLInputElement>('smoothness-slider'),
      smoothnessValue: getElementById<HTMLSpanElement>('smoothness-value'),
      rerollBtn: getElementById<HTMLButtonElement>('reroll-btn'),
      exportPngBtn: getElementById<HTMLButtonElement>('export-png-btn'),
      exportSvgBtn: getElementById<HTMLButtonElement>('export-svg-btn'),
    };
  }

  private setupEventListeners(): void {
    // Seed input
    this.elements.seedInput.addEventListener('input', () => {
      const seed = parseInt(this.elements.seedInput.value) || 1;
      this.config.onParamsChange({ seed });
    });

    // Point count slider
    this.elements.pointCountSlider.addEventListener('input', () => {
      const pointCount = parseInt(this.elements.pointCountSlider.value);
      this.elements.pointCountValue.textContent = pointCount.toString();
      this.config.onParamsChange({ pointCount });
    });

    // Concavity slider
    this.elements.concavitySlider.addEventListener('input', () => {
      const concavity = parseFloat(this.elements.concavitySlider.value);
      this.elements.concavityValue.textContent = concavity.toFixed(1);
      this.config.onParamsChange({ concavity });
    });

    // Smoothness slider
    this.elements.smoothnessSlider.addEventListener('input', () => {
      const smoothness = parseInt(this.elements.smoothnessSlider.value);
      this.elements.smoothnessValue.textContent = smoothness.toString();
      this.config.onParamsChange({ smoothness });
    });

    // Reroll button
    this.elements.rerollBtn.addEventListener('click', () => {
      this.generateNewSeed();
      this.config.onReroll();
    });

    // Export buttons
    this.elements.exportPngBtn.addEventListener('click', () => {
      this.config.onExportPNG();
    });

    this.elements.exportSvgBtn.addEventListener('click', () => {
      this.config.onExportSVG();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (event) => {
      if (event.target instanceof HTMLInputElement) {
        return; // Don't handle shortcuts when typing in inputs
      }

      switch (event.key.toLowerCase()) {
        case 'r':
          event.preventDefault();
          this.generateNewSeed();
          this.config.onReroll();
          break;
        case 'p':
          event.preventDefault();
          this.config.onExportPNG();
          break;
        case 's':
          event.preventDefault();
          this.config.onExportSVG();
          break;
      }
    });
  }

  private generateNewSeed(): void {
    const newSeed = Math.floor(Math.random() * 999999) + 1;
    this.elements.seedInput.value = newSeed.toString();
    this.config.onParamsChange({ seed: newSeed });
  }

  private updateDisplayValues(): void {
    this.elements.pointCountValue.textContent = this.elements.pointCountSlider.value;
    this.elements.concavityValue.textContent = parseFloat(this.elements.concavitySlider.value).toFixed(1);
    this.elements.smoothnessValue.textContent = this.elements.smoothnessSlider.value;
  }

  /**
   * Get current parameter values from the UI
   */
  getCurrentParams(): MapGenerationParams {
    return {
      seed: parseInt(this.elements.seedInput.value) || 1,
      pointCount: parseInt(this.elements.pointCountSlider.value),
      concavity: parseFloat(this.elements.concavitySlider.value),
      smoothness: parseInt(this.elements.smoothnessSlider.value),
      width: 800, // Will be updated by main app
      height: 600
    };
  }

  /**
   * Update UI to reflect new parameters
   */
  setParams(params: Partial<MapGenerationParams>): void {
    if (params.seed !== undefined) {
      this.elements.seedInput.value = params.seed.toString();
    }
    if (params.pointCount !== undefined) {
      this.elements.pointCountSlider.value = params.pointCount.toString();
      this.elements.pointCountValue.textContent = params.pointCount.toString();
    }
    if (params.concavity !== undefined) {
      this.elements.concavitySlider.value = params.concavity.toString();
      this.elements.concavityValue.textContent = params.concavity.toFixed(1);
    }
    if (params.smoothness !== undefined) {
      this.elements.smoothnessSlider.value = params.smoothness.toString();
      this.elements.smoothnessValue.textContent = params.smoothness.toString();
    }
  }

  /**
   * Show loading state
   */
  setLoading(loading: boolean): void {
    const buttons = [this.elements.rerollBtn, this.elements.exportPngBtn, this.elements.exportSvgBtn];
    const inputs = [
      this.elements.seedInput,
      this.elements.pointCountSlider,
      this.elements.concavitySlider,
      this.elements.smoothnessSlider
    ];

    buttons.forEach(btn => {
      btn.disabled = loading;
      if (loading) {
        btn.style.opacity = '0.6';
        btn.style.cursor = 'not-allowed';
      } else {
        btn.style.opacity = '1';
        btn.style.cursor = 'pointer';
      }
    });

    inputs.forEach(input => {
      input.disabled = loading;
      if (loading) {
        input.style.opacity = '0.6';
      } else {
        input.style.opacity = '1';
      }
    });
  }

  /**
   * Show notification message
   */
  showNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded shadow-lg z-50 transition-all duration-300 ${
      type === 'success' ? 'bg-green-500 text-white' :
      type === 'error' ? 'bg-red-500 text-white' :
      'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
      notification.style.opacity = '1';
    }, 10);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }
}
