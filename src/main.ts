import './style.css';
import type { MapGenerationParams } from './mapGenerator';
import { MapGenerator } from './mapGenerator';
import { MapRenderer } from './renderer';
import { Controls } from './controls';

class RiceScatterApp {
  private mapGenerator: MapGenerator;
  private renderer: MapRenderer;
  private controls: Controls;
  private canvas: HTMLCanvasElement;

  constructor() {
    this.canvas = document.getElementById('map-canvas') as HTMLCanvasElement;
    if (!this.canvas) {
      throw new Error('Canvas element not found');
    }

    // Initialize with default parameters
    const defaultParams: MapGenerationParams = {
      seed: 12345,
      landmassType: 'island',
      complexity: 0.5,
      smoothness: 1,
      width: 800,
      height: 600
    };

    this.mapGenerator = new MapGenerator(defaultParams);
    this.renderer = new MapRenderer(this.canvas);

    this.controls = new Controls({
      onParamsChange: this.onParamsChange.bind(this),
      onReroll: this.onReroll.bind(this),
      onExportPNG: this.onExportPNG.bind(this),
      onExportSVG: this.onExportSVG.bind(this)
    });

    this.updateCanvasSize();
    this.generateInitialMap();

    // Handle window resize
    window.addEventListener('resize', this.updateCanvasSize.bind(this));
  }

  private updateCanvasSize(): void {
    const rect = this.canvas.getBoundingClientRect();
    const params = this.mapGenerator.getParams();
    params.width = rect.width;
    params.height = rect.height;
    this.mapGenerator.updateParams(params);
  }

  private onParamsChange(newParams: Partial<MapGenerationParams>): void {
    try {
      this.controls.setLoading(true);

      // Update canvas size if needed
      const rect = this.canvas.getBoundingClientRect();
      const fullParams = {
        ...newParams,
        width: rect.width,
        height: rect.height
      };

      const map = this.mapGenerator.updateParams(fullParams);
      this.renderer.setMap(map);
      this.renderer.resetView();

      this.controls.showNotification('Map updated!', 'success');
    } catch (error) {
      console.error('Error updating map:', error);
      this.controls.showNotification('Error updating map', 'error');
    } finally {
      this.controls.setLoading(false);
    }
  }

  private onReroll(): void {
    try {
      this.controls.setLoading(true);

      // Generate new seed
      const newSeed = Math.floor(Math.random() * 999999) + 1;
      this.controls.setParams({ seed: newSeed });

      const map = this.mapGenerator.updateParams({ seed: newSeed });
      this.renderer.setMap(map);
      this.renderer.resetView();

      this.controls.showNotification(`New map generated! Seed: ${newSeed}`, 'success');
    } catch (error) {
      console.error('Error generating new map:', error);
      this.controls.showNotification('Error generating new map', 'error');
    } finally {
      this.controls.setLoading(false);
    }
  }

  private onExportPNG(): void {
    try {
      this.renderer.exportPNG();
      this.controls.showNotification('PNG exported!', 'success');
    } catch (error) {
      console.error('Error exporting PNG:', error);
      this.controls.showNotification('Error exporting PNG', 'error');
    }
  }

  private onExportSVG(): void {
    try {
      this.renderer.exportSVG();
      this.controls.showNotification('SVG exported!', 'success');
    } catch (error) {
      console.error('Error exporting SVG:', error);
      this.controls.showNotification('Error exporting SVG', 'error');
    }
  }

  private generateInitialMap(): void {
    try {
      const map = this.mapGenerator.generateMap();
      this.renderer.setMap(map);
      this.renderer.resetView();
    } catch (error) {
      console.error('Error generating initial map:', error);
      this.controls.showNotification('Error generating initial map', 'error');
    }
  }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new RiceScatterApp();
});
