import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-DZZM6G22.js";

// node_modules/rbush/rbush.min.js
var require_rbush_min = __commonJS({
  "node_modules/rbush/rbush.min.js"(exports, module) {
    !function(t, i) {
      "object" == typeof exports && "undefined" != typeof module ? module.exports = i() : "function" == typeof define && define.amd ? define(i) : (t = t || self).RBush = i();
    }(exports, function() {
      "use strict";
      function t(t2, r2, e2, a2, h2) {
        !function t3(n2, r3, e3, a3, h3) {
          for (; a3 > e3; ) {
            if (a3 - e3 > 600) {
              var o2 = a3 - e3 + 1, s2 = r3 - e3 + 1, l2 = Math.log(o2), f2 = 0.5 * Math.exp(2 * l2 / 3), u2 = 0.5 * Math.sqrt(l2 * f2 * (o2 - f2) / o2) * (s2 - o2 / 2 < 0 ? -1 : 1), m2 = Math.max(e3, Math.floor(r3 - s2 * f2 / o2 + u2)), c2 = Math.min(a3, Math.floor(r3 + (o2 - s2) * f2 / o2 + u2));
              t3(n2, r3, m2, c2, h3);
            }
            var p2 = n2[r3], d2 = e3, x = a3;
            for (i(n2, e3, r3), h3(n2[a3], p2) > 0 && i(n2, e3, a3); d2 < x; ) {
              for (i(n2, d2, x), d2++, x--; h3(n2[d2], p2) < 0; ) d2++;
              for (; h3(n2[x], p2) > 0; ) x--;
            }
            0 === h3(n2[e3], p2) ? i(n2, e3, x) : i(n2, ++x, a3), x <= r3 && (e3 = x + 1), r3 <= x && (a3 = x - 1);
          }
        }(t2, r2, e2 || 0, a2 || t2.length - 1, h2 || n);
      }
      function i(t2, i2, n2) {
        var r2 = t2[i2];
        t2[i2] = t2[n2], t2[n2] = r2;
      }
      function n(t2, i2) {
        return t2 < i2 ? -1 : t2 > i2 ? 1 : 0;
      }
      var r = function(t2) {
        void 0 === t2 && (t2 = 9), this._maxEntries = Math.max(4, t2), this._minEntries = Math.max(2, Math.ceil(0.4 * this._maxEntries)), this.clear();
      };
      function e(t2, i2, n2) {
        if (!n2) return i2.indexOf(t2);
        for (var r2 = 0; r2 < i2.length; r2++) if (n2(t2, i2[r2])) return r2;
        return -1;
      }
      function a(t2, i2) {
        h(t2, 0, t2.children.length, i2, t2);
      }
      function h(t2, i2, n2, r2, e2) {
        e2 || (e2 = p(null)), e2.minX = 1 / 0, e2.minY = 1 / 0, e2.maxX = -1 / 0, e2.maxY = -1 / 0;
        for (var a2 = i2; a2 < n2; a2++) {
          var h2 = t2.children[a2];
          o(e2, t2.leaf ? r2(h2) : h2);
        }
        return e2;
      }
      function o(t2, i2) {
        return t2.minX = Math.min(t2.minX, i2.minX), t2.minY = Math.min(t2.minY, i2.minY), t2.maxX = Math.max(t2.maxX, i2.maxX), t2.maxY = Math.max(t2.maxY, i2.maxY), t2;
      }
      function s(t2, i2) {
        return t2.minX - i2.minX;
      }
      function l(t2, i2) {
        return t2.minY - i2.minY;
      }
      function f(t2) {
        return (t2.maxX - t2.minX) * (t2.maxY - t2.minY);
      }
      function u(t2) {
        return t2.maxX - t2.minX + (t2.maxY - t2.minY);
      }
      function m(t2, i2) {
        return t2.minX <= i2.minX && t2.minY <= i2.minY && i2.maxX <= t2.maxX && i2.maxY <= t2.maxY;
      }
      function c(t2, i2) {
        return i2.minX <= t2.maxX && i2.minY <= t2.maxY && i2.maxX >= t2.minX && i2.maxY >= t2.minY;
      }
      function p(t2) {
        return { children: t2, height: 1, leaf: true, minX: 1 / 0, minY: 1 / 0, maxX: -1 / 0, maxY: -1 / 0 };
      }
      function d(i2, n2, r2, e2, a2) {
        for (var h2 = [n2, r2]; h2.length; ) if (!((r2 = h2.pop()) - (n2 = h2.pop()) <= e2)) {
          var o2 = n2 + Math.ceil((r2 - n2) / e2 / 2) * e2;
          t(i2, o2, n2, r2, a2), h2.push(n2, o2, o2, r2);
        }
      }
      return r.prototype.all = function() {
        return this._all(this.data, []);
      }, r.prototype.search = function(t2) {
        var i2 = this.data, n2 = [];
        if (!c(t2, i2)) return n2;
        for (var r2 = this.toBBox, e2 = []; i2; ) {
          for (var a2 = 0; a2 < i2.children.length; a2++) {
            var h2 = i2.children[a2], o2 = i2.leaf ? r2(h2) : h2;
            c(t2, o2) && (i2.leaf ? n2.push(h2) : m(t2, o2) ? this._all(h2, n2) : e2.push(h2));
          }
          i2 = e2.pop();
        }
        return n2;
      }, r.prototype.collides = function(t2) {
        var i2 = this.data;
        if (!c(t2, i2)) return false;
        for (var n2 = []; i2; ) {
          for (var r2 = 0; r2 < i2.children.length; r2++) {
            var e2 = i2.children[r2], a2 = i2.leaf ? this.toBBox(e2) : e2;
            if (c(t2, a2)) {
              if (i2.leaf || m(t2, a2)) return true;
              n2.push(e2);
            }
          }
          i2 = n2.pop();
        }
        return false;
      }, r.prototype.load = function(t2) {
        if (!t2 || !t2.length) return this;
        if (t2.length < this._minEntries) {
          for (var i2 = 0; i2 < t2.length; i2++) this.insert(t2[i2]);
          return this;
        }
        var n2 = this._build(t2.slice(), 0, t2.length - 1, 0);
        if (this.data.children.length) if (this.data.height === n2.height) this._splitRoot(this.data, n2);
        else {
          if (this.data.height < n2.height) {
            var r2 = this.data;
            this.data = n2, n2 = r2;
          }
          this._insert(n2, this.data.height - n2.height - 1, true);
        }
        else this.data = n2;
        return this;
      }, r.prototype.insert = function(t2) {
        return t2 && this._insert(t2, this.data.height - 1), this;
      }, r.prototype.clear = function() {
        return this.data = p([]), this;
      }, r.prototype.remove = function(t2, i2) {
        if (!t2) return this;
        for (var n2, r2, a2, h2 = this.data, o2 = this.toBBox(t2), s2 = [], l2 = []; h2 || s2.length; ) {
          if (h2 || (h2 = s2.pop(), r2 = s2[s2.length - 1], n2 = l2.pop(), a2 = true), h2.leaf) {
            var f2 = e(t2, h2.children, i2);
            if (-1 !== f2) return h2.children.splice(f2, 1), s2.push(h2), this._condense(s2), this;
          }
          a2 || h2.leaf || !m(h2, o2) ? r2 ? (n2++, h2 = r2.children[n2], a2 = false) : h2 = null : (s2.push(h2), l2.push(n2), n2 = 0, r2 = h2, h2 = h2.children[0]);
        }
        return this;
      }, r.prototype.toBBox = function(t2) {
        return t2;
      }, r.prototype.compareMinX = function(t2, i2) {
        return t2.minX - i2.minX;
      }, r.prototype.compareMinY = function(t2, i2) {
        return t2.minY - i2.minY;
      }, r.prototype.toJSON = function() {
        return this.data;
      }, r.prototype.fromJSON = function(t2) {
        return this.data = t2, this;
      }, r.prototype._all = function(t2, i2) {
        for (var n2 = []; t2; ) t2.leaf ? i2.push.apply(i2, t2.children) : n2.push.apply(n2, t2.children), t2 = n2.pop();
        return i2;
      }, r.prototype._build = function(t2, i2, n2, r2) {
        var e2, h2 = n2 - i2 + 1, o2 = this._maxEntries;
        if (h2 <= o2) return a(e2 = p(t2.slice(i2, n2 + 1)), this.toBBox), e2;
        r2 || (r2 = Math.ceil(Math.log(h2) / Math.log(o2)), o2 = Math.ceil(h2 / Math.pow(o2, r2 - 1))), (e2 = p([])).leaf = false, e2.height = r2;
        var s2 = Math.ceil(h2 / o2), l2 = s2 * Math.ceil(Math.sqrt(o2));
        d(t2, i2, n2, l2, this.compareMinX);
        for (var f2 = i2; f2 <= n2; f2 += l2) {
          var u2 = Math.min(f2 + l2 - 1, n2);
          d(t2, f2, u2, s2, this.compareMinY);
          for (var m2 = f2; m2 <= u2; m2 += s2) {
            var c2 = Math.min(m2 + s2 - 1, u2);
            e2.children.push(this._build(t2, m2, c2, r2 - 1));
          }
        }
        return a(e2, this.toBBox), e2;
      }, r.prototype._chooseSubtree = function(t2, i2, n2, r2) {
        for (; r2.push(i2), !i2.leaf && r2.length - 1 !== n2; ) {
          for (var e2 = 1 / 0, a2 = 1 / 0, h2 = void 0, o2 = 0; o2 < i2.children.length; o2++) {
            var s2 = i2.children[o2], l2 = f(s2), u2 = (m2 = t2, c2 = s2, (Math.max(c2.maxX, m2.maxX) - Math.min(c2.minX, m2.minX)) * (Math.max(c2.maxY, m2.maxY) - Math.min(c2.minY, m2.minY)) - l2);
            u2 < a2 ? (a2 = u2, e2 = l2 < e2 ? l2 : e2, h2 = s2) : u2 === a2 && l2 < e2 && (e2 = l2, h2 = s2);
          }
          i2 = h2 || i2.children[0];
        }
        var m2, c2;
        return i2;
      }, r.prototype._insert = function(t2, i2, n2) {
        var r2 = n2 ? t2 : this.toBBox(t2), e2 = [], a2 = this._chooseSubtree(r2, this.data, i2, e2);
        for (a2.children.push(t2), o(a2, r2); i2 >= 0 && e2[i2].children.length > this._maxEntries; ) this._split(e2, i2), i2--;
        this._adjustParentBBoxes(r2, e2, i2);
      }, r.prototype._split = function(t2, i2) {
        var n2 = t2[i2], r2 = n2.children.length, e2 = this._minEntries;
        this._chooseSplitAxis(n2, e2, r2);
        var h2 = this._chooseSplitIndex(n2, e2, r2), o2 = p(n2.children.splice(h2, n2.children.length - h2));
        o2.height = n2.height, o2.leaf = n2.leaf, a(n2, this.toBBox), a(o2, this.toBBox), i2 ? t2[i2 - 1].children.push(o2) : this._splitRoot(n2, o2);
      }, r.prototype._splitRoot = function(t2, i2) {
        this.data = p([t2, i2]), this.data.height = t2.height + 1, this.data.leaf = false, a(this.data, this.toBBox);
      }, r.prototype._chooseSplitIndex = function(t2, i2, n2) {
        for (var r2, e2, a2, o2, s2, l2, u2, m2 = 1 / 0, c2 = 1 / 0, p2 = i2; p2 <= n2 - i2; p2++) {
          var d2 = h(t2, 0, p2, this.toBBox), x = h(t2, p2, n2, this.toBBox), v = (e2 = d2, a2 = x, o2 = void 0, s2 = void 0, l2 = void 0, u2 = void 0, o2 = Math.max(e2.minX, a2.minX), s2 = Math.max(e2.minY, a2.minY), l2 = Math.min(e2.maxX, a2.maxX), u2 = Math.min(e2.maxY, a2.maxY), Math.max(0, l2 - o2) * Math.max(0, u2 - s2)), M = f(d2) + f(x);
          v < m2 ? (m2 = v, r2 = p2, c2 = M < c2 ? M : c2) : v === m2 && M < c2 && (c2 = M, r2 = p2);
        }
        return r2 || n2 - i2;
      }, r.prototype._chooseSplitAxis = function(t2, i2, n2) {
        var r2 = t2.leaf ? this.compareMinX : s, e2 = t2.leaf ? this.compareMinY : l;
        this._allDistMargin(t2, i2, n2, r2) < this._allDistMargin(t2, i2, n2, e2) && t2.children.sort(r2);
      }, r.prototype._allDistMargin = function(t2, i2, n2, r2) {
        t2.children.sort(r2);
        for (var e2 = this.toBBox, a2 = h(t2, 0, i2, e2), s2 = h(t2, n2 - i2, n2, e2), l2 = u(a2) + u(s2), f2 = i2; f2 < n2 - i2; f2++) {
          var m2 = t2.children[f2];
          o(a2, t2.leaf ? e2(m2) : m2), l2 += u(a2);
        }
        for (var c2 = n2 - i2 - 1; c2 >= i2; c2--) {
          var p2 = t2.children[c2];
          o(s2, t2.leaf ? e2(p2) : p2), l2 += u(s2);
        }
        return l2;
      }, r.prototype._adjustParentBBoxes = function(t2, i2, n2) {
        for (var r2 = n2; r2 >= 0; r2--) o(i2[r2], t2);
      }, r.prototype._condense = function(t2) {
        for (var i2 = t2.length - 1, n2 = void 0; i2 >= 0; i2--) 0 === t2[i2].children.length ? i2 > 0 ? (n2 = t2[i2 - 1].children).splice(n2.indexOf(t2[i2]), 1) : this.clear() : a(t2[i2], this.toBBox);
      }, r;
    });
  }
});

// node_modules/tinyqueue/index.js
var tinyqueue_exports = {};
__export(tinyqueue_exports, {
  default: () => TinyQueue
});
function defaultCompare(a, b) {
  return a < b ? -1 : a > b ? 1 : 0;
}
var TinyQueue;
var init_tinyqueue = __esm({
  "node_modules/tinyqueue/index.js"() {
    TinyQueue = class {
      constructor(data = [], compare = defaultCompare) {
        this.data = data;
        this.length = this.data.length;
        this.compare = compare;
        if (this.length > 0) {
          for (let i = (this.length >> 1) - 1; i >= 0; i--) this._down(i);
        }
      }
      push(item) {
        this.data.push(item);
        this.length++;
        this._up(this.length - 1);
      }
      pop() {
        if (this.length === 0) return void 0;
        const top = this.data[0];
        const bottom = this.data.pop();
        this.length--;
        if (this.length > 0) {
          this.data[0] = bottom;
          this._down(0);
        }
        return top;
      }
      peek() {
        return this.data[0];
      }
      _up(pos) {
        const { data, compare } = this;
        const item = data[pos];
        while (pos > 0) {
          const parent = pos - 1 >> 1;
          const current = data[parent];
          if (compare(item, current) >= 0) break;
          data[pos] = current;
          pos = parent;
        }
        data[pos] = item;
      }
      _down(pos) {
        const { data, compare } = this;
        const halfLength = this.length >> 1;
        const item = data[pos];
        while (pos < halfLength) {
          let left = (pos << 1) + 1;
          let best = data[left];
          const right = left + 1;
          if (right < this.length && compare(data[right], best) < 0) {
            left = right;
            best = data[right];
          }
          if (compare(best, item) >= 0) break;
          data[pos] = best;
          pos = left;
        }
        data[pos] = item;
      }
    };
  }
});

// node_modules/point-in-polygon/flat.js
var require_flat = __commonJS({
  "node_modules/point-in-polygon/flat.js"(exports, module) {
    module.exports = function pointInPolygonFlat(point, vs, start, end) {
      var x = point[0], y = point[1];
      var inside = false;
      if (start === void 0) start = 0;
      if (end === void 0) end = vs.length;
      var len = (end - start) / 2;
      for (var i = 0, j = len - 1; i < len; j = i++) {
        var xi = vs[start + i * 2 + 0], yi = vs[start + i * 2 + 1];
        var xj = vs[start + j * 2 + 0], yj = vs[start + j * 2 + 1];
        var intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;
        if (intersect) inside = !inside;
      }
      return inside;
    };
  }
});

// node_modules/point-in-polygon/nested.js
var require_nested = __commonJS({
  "node_modules/point-in-polygon/nested.js"(exports, module) {
    module.exports = function pointInPolygonNested(point, vs, start, end) {
      var x = point[0], y = point[1];
      var inside = false;
      if (start === void 0) start = 0;
      if (end === void 0) end = vs.length;
      var len = end - start;
      for (var i = 0, j = len - 1; i < len; j = i++) {
        var xi = vs[i + start][0], yi = vs[i + start][1];
        var xj = vs[j + start][0], yj = vs[j + start][1];
        var intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;
        if (intersect) inside = !inside;
      }
      return inside;
    };
  }
});

// node_modules/point-in-polygon/index.js
var require_point_in_polygon = __commonJS({
  "node_modules/point-in-polygon/index.js"(exports, module) {
    var pointInPolygonFlat = require_flat();
    var pointInPolygonNested = require_nested();
    module.exports = function pointInPolygon(point, vs, start, end) {
      if (vs.length > 0 && Array.isArray(vs[0])) {
        return pointInPolygonNested(point, vs, start, end);
      } else {
        return pointInPolygonFlat(point, vs, start, end);
      }
    };
    module.exports.nested = pointInPolygonNested;
    module.exports.flat = pointInPolygonFlat;
  }
});

// node_modules/robust-predicates/umd/orient2d.min.js
var require_orient2d_min = __commonJS({
  "node_modules/robust-predicates/umd/orient2d.min.js"(exports, module) {
    !function(t, e) {
      "object" == typeof exports && "undefined" != typeof module ? e(exports) : "function" == typeof define && define.amd ? define(["exports"], e) : e((t = t || self).predicates = {});
    }(exports, function(t) {
      "use strict";
      const e = 134217729, n = 33306690738754706e-32;
      function r(t2, e2, n2, r2, o2) {
        let f2, i2, u2, c2, s2 = e2[0], a2 = r2[0], d2 = 0, l2 = 0;
        a2 > s2 == a2 > -s2 ? (f2 = s2, s2 = e2[++d2]) : (f2 = a2, a2 = r2[++l2]);
        let p = 0;
        if (d2 < t2 && l2 < n2) for (a2 > s2 == a2 > -s2 ? (u2 = f2 - ((i2 = s2 + f2) - s2), s2 = e2[++d2]) : (u2 = f2 - ((i2 = a2 + f2) - a2), a2 = r2[++l2]), f2 = i2, 0 !== u2 && (o2[p++] = u2); d2 < t2 && l2 < n2; ) a2 > s2 == a2 > -s2 ? (u2 = f2 - ((i2 = f2 + s2) - (c2 = i2 - f2)) + (s2 - c2), s2 = e2[++d2]) : (u2 = f2 - ((i2 = f2 + a2) - (c2 = i2 - f2)) + (a2 - c2), a2 = r2[++l2]), f2 = i2, 0 !== u2 && (o2[p++] = u2);
        for (; d2 < t2; ) u2 = f2 - ((i2 = f2 + s2) - (c2 = i2 - f2)) + (s2 - c2), s2 = e2[++d2], f2 = i2, 0 !== u2 && (o2[p++] = u2);
        for (; l2 < n2; ) u2 = f2 - ((i2 = f2 + a2) - (c2 = i2 - f2)) + (a2 - c2), a2 = r2[++l2], f2 = i2, 0 !== u2 && (o2[p++] = u2);
        return 0 === f2 && 0 !== p || (o2[p++] = f2), p;
      }
      function o(t2) {
        return new Float64Array(t2);
      }
      const f = 33306690738754716e-32, i = 22204460492503146e-32, u = 11093356479670487e-47, c = o(4), s = o(8), a = o(12), d = o(16), l = o(4);
      t.orient2d = function(t2, o2, p, b, y, h) {
        const M = (o2 - h) * (p - y), x = (t2 - y) * (b - h), j = M - x;
        if (0 === M || 0 === x || M > 0 != x > 0) return j;
        const m = Math.abs(M + x);
        return Math.abs(j) >= f * m ? j : -function(t3, o3, f2, p2, b2, y2, h2) {
          let M2, x2, j2, m2, _, v, w, A, F, O, P, g, k, q, z, B, C, D;
          const E = t3 - b2, G = f2 - b2, H = o3 - y2, I = p2 - y2;
          _ = (z = (A = E - (w = (v = e * E) - (v - E))) * (O = I - (F = (v = e * I) - (v - I))) - ((q = E * I) - w * F - A * F - w * O)) - (P = z - (C = (A = H - (w = (v = e * H) - (v - H))) * (O = G - (F = (v = e * G) - (v - G))) - ((B = H * G) - w * F - A * F - w * O))), c[0] = z - (P + _) + (_ - C), _ = (k = q - ((g = q + P) - (_ = g - q)) + (P - _)) - (P = k - B), c[1] = k - (P + _) + (_ - B), _ = (D = g + P) - g, c[2] = g - (D - _) + (P - _), c[3] = D;
          let J = function(t4, e2) {
            let n2 = e2[0];
            for (let r2 = 1; r2 < t4; r2++) n2 += e2[r2];
            return n2;
          }(4, c), K = i * h2;
          if (J >= K || -J >= K) return J;
          if (M2 = t3 - (E + (_ = t3 - E)) + (_ - b2), j2 = f2 - (G + (_ = f2 - G)) + (_ - b2), x2 = o3 - (H + (_ = o3 - H)) + (_ - y2), m2 = p2 - (I + (_ = p2 - I)) + (_ - y2), 0 === M2 && 0 === x2 && 0 === j2 && 0 === m2) return J;
          if (K = u * h2 + n * Math.abs(J), (J += E * m2 + I * M2 - (H * j2 + G * x2)) >= K || -J >= K) return J;
          _ = (z = (A = M2 - (w = (v = e * M2) - (v - M2))) * (O = I - (F = (v = e * I) - (v - I))) - ((q = M2 * I) - w * F - A * F - w * O)) - (P = z - (C = (A = x2 - (w = (v = e * x2) - (v - x2))) * (O = G - (F = (v = e * G) - (v - G))) - ((B = x2 * G) - w * F - A * F - w * O))), l[0] = z - (P + _) + (_ - C), _ = (k = q - ((g = q + P) - (_ = g - q)) + (P - _)) - (P = k - B), l[1] = k - (P + _) + (_ - B), _ = (D = g + P) - g, l[2] = g - (D - _) + (P - _), l[3] = D;
          const L = r(4, c, 4, l, s);
          _ = (z = (A = E - (w = (v = e * E) - (v - E))) * (O = m2 - (F = (v = e * m2) - (v - m2))) - ((q = E * m2) - w * F - A * F - w * O)) - (P = z - (C = (A = H - (w = (v = e * H) - (v - H))) * (O = j2 - (F = (v = e * j2) - (v - j2))) - ((B = H * j2) - w * F - A * F - w * O))), l[0] = z - (P + _) + (_ - C), _ = (k = q - ((g = q + P) - (_ = g - q)) + (P - _)) - (P = k - B), l[1] = k - (P + _) + (_ - B), _ = (D = g + P) - g, l[2] = g - (D - _) + (P - _), l[3] = D;
          const N = r(L, s, 4, l, a);
          _ = (z = (A = M2 - (w = (v = e * M2) - (v - M2))) * (O = m2 - (F = (v = e * m2) - (v - m2))) - ((q = M2 * m2) - w * F - A * F - w * O)) - (P = z - (C = (A = x2 - (w = (v = e * x2) - (v - x2))) * (O = j2 - (F = (v = e * j2) - (v - j2))) - ((B = x2 * j2) - w * F - A * F - w * O))), l[0] = z - (P + _) + (_ - C), _ = (k = q - ((g = q + P) - (_ = g - q)) + (P - _)) - (P = k - B), l[1] = k - (P + _) + (_ - B), _ = (D = g + P) - g, l[2] = g - (D - _) + (P - _), l[3] = D;
          const Q = r(N, a, 4, l, d);
          return d[Q - 1];
        }(t2, o2, p, b, y, h, m);
      }, t.orient2dfast = function(t2, e2, n2, r2, o2, f2) {
        return (e2 - f2) * (n2 - o2) - (t2 - o2) * (r2 - f2);
      }, Object.defineProperty(t, "__esModule", { value: true });
    });
  }
});

// node_modules/concaveman/index.js
var require_concaveman = __commonJS({
  "node_modules/concaveman/index.js"(exports, module) {
    var RBush = require_rbush_min();
    var Queue = (init_tinyqueue(), __toCommonJS(tinyqueue_exports));
    var pointInPolygon = require_point_in_polygon();
    var orient = require_orient2d_min().orient2d;
    if (Queue.default) {
      Queue = Queue.default;
    }
    module.exports = concaveman;
    module.exports.default = concaveman;
    function concaveman(points, concavity, lengthThreshold) {
      concavity = Math.max(0, concavity === void 0 ? 2 : concavity);
      lengthThreshold = lengthThreshold || 0;
      var hull = fastConvexHull(points);
      var tree = new RBush(16);
      tree.toBBox = function(a2) {
        return {
          minX: a2[0],
          minY: a2[1],
          maxX: a2[0],
          maxY: a2[1]
        };
      };
      tree.compareMinX = function(a2, b2) {
        return a2[0] - b2[0];
      };
      tree.compareMinY = function(a2, b2) {
        return a2[1] - b2[1];
      };
      tree.load(points);
      var queue = [];
      for (var i = 0, last; i < hull.length; i++) {
        var p = hull[i];
        tree.remove(p);
        last = insertNode(p, last);
        queue.push(last);
      }
      var segTree = new RBush(16);
      for (i = 0; i < queue.length; i++) segTree.insert(updateBBox(queue[i]));
      var sqConcavity = concavity * concavity;
      var sqLenThreshold = lengthThreshold * lengthThreshold;
      while (queue.length) {
        var node = queue.shift();
        var a = node.p;
        var b = node.next.p;
        var sqLen = getSqDist(a, b);
        if (sqLen < sqLenThreshold) continue;
        var maxSqLen = sqLen / sqConcavity;
        p = findCandidate(tree, node.prev.p, a, b, node.next.next.p, maxSqLen, segTree);
        if (p && Math.min(getSqDist(p, a), getSqDist(p, b)) <= maxSqLen) {
          queue.push(node);
          queue.push(insertNode(p, node));
          tree.remove(p);
          segTree.remove(node);
          segTree.insert(updateBBox(node));
          segTree.insert(updateBBox(node.next));
        }
      }
      node = last;
      var concave = [];
      do {
        concave.push(node.p);
        node = node.next;
      } while (node !== last);
      concave.push(node.p);
      return concave;
    }
    function findCandidate(tree, a, b, c, d, maxDist, segTree) {
      var queue = new Queue([], compareDist);
      var node = tree.data;
      while (node) {
        for (var i = 0; i < node.children.length; i++) {
          var child = node.children[i];
          var dist = node.leaf ? sqSegDist(child, b, c) : sqSegBoxDist(b, c, child);
          if (dist > maxDist) continue;
          queue.push({
            node: child,
            dist
          });
        }
        while (queue.length && !queue.peek().node.children) {
          var item = queue.pop();
          var p = item.node;
          var d0 = sqSegDist(p, a, b);
          var d1 = sqSegDist(p, c, d);
          if (item.dist < d0 && item.dist < d1 && noIntersections(b, p, segTree) && noIntersections(c, p, segTree)) return p;
        }
        node = queue.pop();
        if (node) node = node.node;
      }
      return null;
    }
    function compareDist(a, b) {
      return a.dist - b.dist;
    }
    function sqSegBoxDist(a, b, bbox) {
      if (inside(a, bbox) || inside(b, bbox)) return 0;
      var d1 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.minX, bbox.minY, bbox.maxX, bbox.minY);
      if (d1 === 0) return 0;
      var d2 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.minX, bbox.minY, bbox.minX, bbox.maxY);
      if (d2 === 0) return 0;
      var d3 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.maxX, bbox.minY, bbox.maxX, bbox.maxY);
      if (d3 === 0) return 0;
      var d4 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.minX, bbox.maxY, bbox.maxX, bbox.maxY);
      if (d4 === 0) return 0;
      return Math.min(d1, d2, d3, d4);
    }
    function inside(a, bbox) {
      return a[0] >= bbox.minX && a[0] <= bbox.maxX && a[1] >= bbox.minY && a[1] <= bbox.maxY;
    }
    function noIntersections(a, b, segTree) {
      var minX = Math.min(a[0], b[0]);
      var minY = Math.min(a[1], b[1]);
      var maxX = Math.max(a[0], b[0]);
      var maxY = Math.max(a[1], b[1]);
      var edges = segTree.search({ minX, minY, maxX, maxY });
      for (var i = 0; i < edges.length; i++) {
        if (intersects(edges[i].p, edges[i].next.p, a, b)) return false;
      }
      return true;
    }
    function cross(p1, p2, p3) {
      return orient(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);
    }
    function intersects(p1, q1, p2, q2) {
      return p1 !== q2 && q1 !== p2 && cross(p1, q1, p2) > 0 !== cross(p1, q1, q2) > 0 && cross(p2, q2, p1) > 0 !== cross(p2, q2, q1) > 0;
    }
    function updateBBox(node) {
      var p1 = node.p;
      var p2 = node.next.p;
      node.minX = Math.min(p1[0], p2[0]);
      node.minY = Math.min(p1[1], p2[1]);
      node.maxX = Math.max(p1[0], p2[0]);
      node.maxY = Math.max(p1[1], p2[1]);
      return node;
    }
    function fastConvexHull(points) {
      var left = points[0];
      var top = points[0];
      var right = points[0];
      var bottom = points[0];
      for (var i = 0; i < points.length; i++) {
        var p = points[i];
        if (p[0] < left[0]) left = p;
        if (p[0] > right[0]) right = p;
        if (p[1] < top[1]) top = p;
        if (p[1] > bottom[1]) bottom = p;
      }
      var cull = [left, top, right, bottom];
      var filtered = cull.slice();
      for (i = 0; i < points.length; i++) {
        if (!pointInPolygon(points[i], cull)) filtered.push(points[i]);
      }
      return convexHull(filtered);
    }
    function insertNode(p, prev) {
      var node = {
        p,
        prev: null,
        next: null,
        minX: 0,
        minY: 0,
        maxX: 0,
        maxY: 0
      };
      if (!prev) {
        node.prev = node;
        node.next = node;
      } else {
        node.next = prev.next;
        node.prev = prev;
        prev.next.prev = node;
        prev.next = node;
      }
      return node;
    }
    function getSqDist(p1, p2) {
      var dx = p1[0] - p2[0], dy = p1[1] - p2[1];
      return dx * dx + dy * dy;
    }
    function sqSegDist(p, p1, p2) {
      var x = p1[0], y = p1[1], dx = p2[0] - x, dy = p2[1] - y;
      if (dx !== 0 || dy !== 0) {
        var t = ((p[0] - x) * dx + (p[1] - y) * dy) / (dx * dx + dy * dy);
        if (t > 1) {
          x = p2[0];
          y = p2[1];
        } else if (t > 0) {
          x += dx * t;
          y += dy * t;
        }
      }
      dx = p[0] - x;
      dy = p[1] - y;
      return dx * dx + dy * dy;
    }
    function sqSegSegDist(x0, y0, x1, y1, x2, y2, x3, y3) {
      var ux = x1 - x0;
      var uy = y1 - y0;
      var vx = x3 - x2;
      var vy = y3 - y2;
      var wx = x0 - x2;
      var wy = y0 - y2;
      var a = ux * ux + uy * uy;
      var b = ux * vx + uy * vy;
      var c = vx * vx + vy * vy;
      var d = ux * wx + uy * wy;
      var e = vx * wx + vy * wy;
      var D = a * c - b * b;
      var sc, sN, tc, tN;
      var sD = D;
      var tD = D;
      if (D === 0) {
        sN = 0;
        sD = 1;
        tN = e;
        tD = c;
      } else {
        sN = b * e - c * d;
        tN = a * e - b * d;
        if (sN < 0) {
          sN = 0;
          tN = e;
          tD = c;
        } else if (sN > sD) {
          sN = sD;
          tN = e + b;
          tD = c;
        }
      }
      if (tN < 0) {
        tN = 0;
        if (-d < 0) sN = 0;
        else if (-d > a) sN = sD;
        else {
          sN = -d;
          sD = a;
        }
      } else if (tN > tD) {
        tN = tD;
        if (-d + b < 0) sN = 0;
        else if (-d + b > a) sN = sD;
        else {
          sN = -d + b;
          sD = a;
        }
      }
      sc = sN === 0 ? 0 : sN / sD;
      tc = tN === 0 ? 0 : tN / tD;
      var cx = (1 - sc) * x0 + sc * x1;
      var cy = (1 - sc) * y0 + sc * y1;
      var cx2 = (1 - tc) * x2 + tc * x3;
      var cy2 = (1 - tc) * y2 + tc * y3;
      var dx = cx2 - cx;
      var dy = cy2 - cy;
      return dx * dx + dy * dy;
    }
    function compareByX(a, b) {
      return a[0] === b[0] ? a[1] - b[1] : a[0] - b[0];
    }
    function convexHull(points) {
      points.sort(compareByX);
      var lower = [];
      for (var i = 0; i < points.length; i++) {
        while (lower.length >= 2 && cross(lower[lower.length - 2], lower[lower.length - 1], points[i]) <= 0) {
          lower.pop();
        }
        lower.push(points[i]);
      }
      var upper = [];
      for (var ii = points.length - 1; ii >= 0; ii--) {
        while (upper.length >= 2 && cross(upper[upper.length - 2], upper[upper.length - 1], points[ii]) <= 0) {
          upper.pop();
        }
        upper.push(points[ii]);
      }
      upper.pop();
      lower.pop();
      return lower.concat(upper);
    }
  }
});
export default require_concaveman();
//# sourceMappingURL=concaveman.js.map
