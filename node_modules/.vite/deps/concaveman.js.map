{"version": 3, "sources": ["../../rbush/rbush.min.js", "../../tinyqueue/index.js", "../../point-in-polygon/flat.js", "../../point-in-polygon/nested.js", "../../point-in-polygon/index.js", "../../robust-predicates/umd/orient2d.min.js", "../../concaveman/index.js"], "sourcesContent": ["!function(t,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(t=t||self).RBush=i()}(this,function(){\"use strict\";function t(t,r,e,a,h){!function t(n,r,e,a,h){for(;a>e;){if(a-e>600){var o=a-e+1,s=r-e+1,l=Math.log(o),f=.5*Math.exp(2*l/3),u=.5*Math.sqrt(l*f*(o-f)/o)*(s-o/2<0?-1:1),m=Math.max(e,Math.floor(r-s*f/o+u)),c=Math.min(a,Math.floor(r+(o-s)*f/o+u));t(n,r,m,c,h)}var p=n[r],d=e,x=a;for(i(n,e,r),h(n[a],p)>0&&i(n,e,a);d<x;){for(i(n,d,x),d++,x--;h(n[d],p)<0;)d++;for(;h(n[x],p)>0;)x--}0===h(n[e],p)?i(n,e,x):i(n,++x,a),x<=r&&(e=x+1),r<=x&&(a=x-1)}}(t,r,e||0,a||t.length-1,h||n)}function i(t,i,n){var r=t[i];t[i]=t[n],t[n]=r}function n(t,i){return t<i?-1:t>i?1:0}var r=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function e(t,i,n){if(!n)return i.indexOf(t);for(var r=0;r<i.length;r++)if(n(t,i[r]))return r;return-1}function a(t,i){h(t,0,t.children.length,i,t)}function h(t,i,n,r,e){e||(e=p(null)),e.minX=1/0,e.minY=1/0,e.maxX=-1/0,e.maxY=-1/0;for(var a=i;a<n;a++){var h=t.children[a];o(e,t.leaf?r(h):h)}return e}function o(t,i){return t.minX=Math.min(t.minX,i.minX),t.minY=Math.min(t.minY,i.minY),t.maxX=Math.max(t.maxX,i.maxX),t.maxY=Math.max(t.maxY,i.maxY),t}function s(t,i){return t.minX-i.minX}function l(t,i){return t.minY-i.minY}function f(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function u(t){return t.maxX-t.minX+(t.maxY-t.minY)}function m(t,i){return t.minX<=i.minX&&t.minY<=i.minY&&i.maxX<=t.maxX&&i.maxY<=t.maxY}function c(t,i){return i.minX<=t.maxX&&i.minY<=t.maxY&&i.maxX>=t.minX&&i.maxY>=t.minY}function p(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function d(i,n,r,e,a){for(var h=[n,r];h.length;)if(!((r=h.pop())-(n=h.pop())<=e)){var o=n+Math.ceil((r-n)/e/2)*e;t(i,o,n,r,a),h.push(n,o,o,r)}}return r.prototype.all=function(){return this._all(this.data,[])},r.prototype.search=function(t){var i=this.data,n=[];if(!c(t,i))return n;for(var r=this.toBBox,e=[];i;){for(var a=0;a<i.children.length;a++){var h=i.children[a],o=i.leaf?r(h):h;c(t,o)&&(i.leaf?n.push(h):m(t,o)?this._all(h,n):e.push(h))}i=e.pop()}return n},r.prototype.collides=function(t){var i=this.data;if(!c(t,i))return!1;for(var n=[];i;){for(var r=0;r<i.children.length;r++){var e=i.children[r],a=i.leaf?this.toBBox(e):e;if(c(t,a)){if(i.leaf||m(t,a))return!0;n.push(e)}}i=n.pop()}return!1},r.prototype.load=function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var i=0;i<t.length;i++)this.insert(t[i]);return this}var n=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===n.height)this._splitRoot(this.data,n);else{if(this.data.height<n.height){var r=this.data;this.data=n,n=r}this._insert(n,this.data.height-n.height-1,!0)}else this.data=n;return this},r.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},r.prototype.clear=function(){return this.data=p([]),this},r.prototype.remove=function(t,i){if(!t)return this;for(var n,r,a,h=this.data,o=this.toBBox(t),s=[],l=[];h||s.length;){if(h||(h=s.pop(),r=s[s.length-1],n=l.pop(),a=!0),h.leaf){var f=e(t,h.children,i);if(-1!==f)return h.children.splice(f,1),s.push(h),this._condense(s),this}a||h.leaf||!m(h,o)?r?(n++,h=r.children[n],a=!1):h=null:(s.push(h),l.push(n),n=0,r=h,h=h.children[0])}return this},r.prototype.toBBox=function(t){return t},r.prototype.compareMinX=function(t,i){return t.minX-i.minX},r.prototype.compareMinY=function(t,i){return t.minY-i.minY},r.prototype.toJSON=function(){return this.data},r.prototype.fromJSON=function(t){return this.data=t,this},r.prototype._all=function(t,i){for(var n=[];t;)t.leaf?i.push.apply(i,t.children):n.push.apply(n,t.children),t=n.pop();return i},r.prototype._build=function(t,i,n,r){var e,h=n-i+1,o=this._maxEntries;if(h<=o)return a(e=p(t.slice(i,n+1)),this.toBBox),e;r||(r=Math.ceil(Math.log(h)/Math.log(o)),o=Math.ceil(h/Math.pow(o,r-1))),(e=p([])).leaf=!1,e.height=r;var s=Math.ceil(h/o),l=s*Math.ceil(Math.sqrt(o));d(t,i,n,l,this.compareMinX);for(var f=i;f<=n;f+=l){var u=Math.min(f+l-1,n);d(t,f,u,s,this.compareMinY);for(var m=f;m<=u;m+=s){var c=Math.min(m+s-1,u);e.children.push(this._build(t,m,c,r-1))}}return a(e,this.toBBox),e},r.prototype._chooseSubtree=function(t,i,n,r){for(;r.push(i),!i.leaf&&r.length-1!==n;){for(var e=1/0,a=1/0,h=void 0,o=0;o<i.children.length;o++){var s=i.children[o],l=f(s),u=(m=t,c=s,(Math.max(c.maxX,m.maxX)-Math.min(c.minX,m.minX))*(Math.max(c.maxY,m.maxY)-Math.min(c.minY,m.minY))-l);u<a?(a=u,e=l<e?l:e,h=s):u===a&&l<e&&(e=l,h=s)}i=h||i.children[0]}var m,c;return i},r.prototype._insert=function(t,i,n){var r=n?t:this.toBBox(t),e=[],a=this._chooseSubtree(r,this.data,i,e);for(a.children.push(t),o(a,r);i>=0&&e[i].children.length>this._maxEntries;)this._split(e,i),i--;this._adjustParentBBoxes(r,e,i)},r.prototype._split=function(t,i){var n=t[i],r=n.children.length,e=this._minEntries;this._chooseSplitAxis(n,e,r);var h=this._chooseSplitIndex(n,e,r),o=p(n.children.splice(h,n.children.length-h));o.height=n.height,o.leaf=n.leaf,a(n,this.toBBox),a(o,this.toBBox),i?t[i-1].children.push(o):this._splitRoot(n,o)},r.prototype._splitRoot=function(t,i){this.data=p([t,i]),this.data.height=t.height+1,this.data.leaf=!1,a(this.data,this.toBBox)},r.prototype._chooseSplitIndex=function(t,i,n){for(var r,e,a,o,s,l,u,m=1/0,c=1/0,p=i;p<=n-i;p++){var d=h(t,0,p,this.toBBox),x=h(t,p,n,this.toBBox),v=(e=d,a=x,o=void 0,s=void 0,l=void 0,u=void 0,o=Math.max(e.minX,a.minX),s=Math.max(e.minY,a.minY),l=Math.min(e.maxX,a.maxX),u=Math.min(e.maxY,a.maxY),Math.max(0,l-o)*Math.max(0,u-s)),M=f(d)+f(x);v<m?(m=v,r=p,c=M<c?M:c):v===m&&M<c&&(c=M,r=p)}return r||n-i},r.prototype._chooseSplitAxis=function(t,i,n){var r=t.leaf?this.compareMinX:s,e=t.leaf?this.compareMinY:l;this._allDistMargin(t,i,n,r)<this._allDistMargin(t,i,n,e)&&t.children.sort(r)},r.prototype._allDistMargin=function(t,i,n,r){t.children.sort(r);for(var e=this.toBBox,a=h(t,0,i,e),s=h(t,n-i,n,e),l=u(a)+u(s),f=i;f<n-i;f++){var m=t.children[f];o(a,t.leaf?e(m):m),l+=u(a)}for(var c=n-i-1;c>=i;c--){var p=t.children[c];o(s,t.leaf?e(p):p),l+=u(s)}return l},r.prototype._adjustParentBBoxes=function(t,i,n){for(var r=n;r>=0;r--)o(i[r],t)},r.prototype._condense=function(t){for(var i=t.length-1,n=void 0;i>=0;i--)0===t[i].children.length?i>0?(n=t[i-1].children).splice(n.indexOf(t[i]),1):this.clear():a(t[i],this.toBBox)},r});\n", "\nexport default class TinyQueue {\n    constructor(data = [], compare = defaultCompare) {\n        this.data = data;\n        this.length = this.data.length;\n        this.compare = compare;\n\n        if (this.length > 0) {\n            for (let i = (this.length >> 1) - 1; i >= 0; i--) this._down(i);\n        }\n    }\n\n    push(item) {\n        this.data.push(item);\n        this.length++;\n        this._up(this.length - 1);\n    }\n\n    pop() {\n        if (this.length === 0) return undefined;\n\n        const top = this.data[0];\n        const bottom = this.data.pop();\n        this.length--;\n\n        if (this.length > 0) {\n            this.data[0] = bottom;\n            this._down(0);\n        }\n\n        return top;\n    }\n\n    peek() {\n        return this.data[0];\n    }\n\n    _up(pos) {\n        const {data, compare} = this;\n        const item = data[pos];\n\n        while (pos > 0) {\n            const parent = (pos - 1) >> 1;\n            const current = data[parent];\n            if (compare(item, current) >= 0) break;\n            data[pos] = current;\n            pos = parent;\n        }\n\n        data[pos] = item;\n    }\n\n    _down(pos) {\n        const {data, compare} = this;\n        const halfLength = this.length >> 1;\n        const item = data[pos];\n\n        while (pos < halfLength) {\n            let left = (pos << 1) + 1;\n            let best = data[left];\n            const right = left + 1;\n\n            if (right < this.length && compare(data[right], best) < 0) {\n                left = right;\n                best = data[right];\n            }\n            if (compare(best, item) >= 0) break;\n\n            data[pos] = best;\n            pos = left;\n        }\n\n        data[pos] = item;\n    }\n}\n\nfunction defaultCompare(a, b) {\n    return a < b ? -1 : a > b ? 1 : 0;\n}\n", "module.exports = function pointInPolygonFlat (point, vs, start, end) {\n    var x = point[0], y = point[1];\n    var inside = false;\n    if (start === undefined) start = 0;\n    if (end === undefined) end = vs.length;\n    var len = (end-start)/2;\n    for (var i = 0, j = len - 1; i < len; j = i++) {\n        var xi = vs[start+i*2+0], yi = vs[start+i*2+1];\n        var xj = vs[start+j*2+0], yj = vs[start+j*2+1];\n        var intersect = ((yi > y) !== (yj > y))\n            && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n        if (intersect) inside = !inside;\n    }\n    return inside;\n};\n", "// ray-casting algorithm based on\n// https://wrf.ecse.rpi.edu/Research/Short_Notes/pnpoly.html\n\nmodule.exports = function pointInPolygonNested (point, vs, start, end) {\n    var x = point[0], y = point[1];\n    var inside = false;\n    if (start === undefined) start = 0;\n    if (end === undefined) end = vs.length;\n    var len = end - start;\n    for (var i = 0, j = len - 1; i < len; j = i++) {\n        var xi = vs[i+start][0], yi = vs[i+start][1];\n        var xj = vs[j+start][0], yj = vs[j+start][1];\n        var intersect = ((yi > y) !== (yj > y))\n            && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n        if (intersect) inside = !inside;\n    }\n    return inside;\n};\n", "var pointInPolygonFlat = require('./flat.js')\nvar pointInPolygonNested = require('./nested.js')\n\nmodule.exports = function pointInPolygon (point, vs, start, end) {\n    if (vs.length > 0 && Array.isArray(vs[0])) {\n        return pointInPolygonNested(point, vs, start, end);\n    } else {\n        return pointInPolygonFlat(point, vs, start, end);\n    }\n}\nmodule.exports.nested = pointInPolygonNested\nmodule.exports.flat = pointInPolygonFlat\n", "!function(t,e){\"object\"==typeof exports&&\"undefined\"!=typeof module?e(exports):\"function\"==typeof define&&define.amd?define([\"exports\"],e):e((t=t||self).predicates={})}(this,function(t){\"use strict\";const e=134217729,n=33306690738754706e-32;function r(t,e,n,r,o){let f,i,u,c,s=e[0],a=r[0],d=0,l=0;a>s==a>-s?(f=s,s=e[++d]):(f=a,a=r[++l]);let p=0;if(d<t&&l<n)for(a>s==a>-s?(u=f-((i=s+f)-s),s=e[++d]):(u=f-((i=a+f)-a),a=r[++l]),f=i,0!==u&&(o[p++]=u);d<t&&l<n;)a>s==a>-s?(u=f-((i=f+s)-(c=i-f))+(s-c),s=e[++d]):(u=f-((i=f+a)-(c=i-f))+(a-c),a=r[++l]),f=i,0!==u&&(o[p++]=u);for(;d<t;)u=f-((i=f+s)-(c=i-f))+(s-c),s=e[++d],f=i,0!==u&&(o[p++]=u);for(;l<n;)u=f-((i=f+a)-(c=i-f))+(a-c),a=r[++l],f=i,0!==u&&(o[p++]=u);return 0===f&&0!==p||(o[p++]=f),p}function o(t){return new Float64Array(t)}const f=33306690738754716e-32,i=22204460492503146e-32,u=11093356479670487e-47,c=o(4),s=o(8),a=o(12),d=o(16),l=o(4);t.orient2d=function(t,o,p,b,y,h){const M=(o-h)*(p-y),x=(t-y)*(b-h),j=M-x;if(0===M||0===x||M>0!=x>0)return j;const m=Math.abs(M+x);return Math.abs(j)>=f*m?j:-function(t,o,f,p,b,y,h){let M,x,j,m,_,v,w,A,F,O,P,g,k,q,z,B,C,D;const E=t-b,G=f-b,H=o-y,I=p-y;_=(z=(A=E-(w=(v=e*E)-(v-E)))*(O=I-(F=(v=e*I)-(v-I)))-((q=E*I)-w*F-A*F-w*O))-(P=z-(C=(A=H-(w=(v=e*H)-(v-H)))*(O=G-(F=(v=e*G)-(v-G)))-((B=H*G)-w*F-A*F-w*O))),c[0]=z-(P+_)+(_-C),_=(k=q-((g=q+P)-(_=g-q))+(P-_))-(P=k-B),c[1]=k-(P+_)+(_-B),_=(D=g+P)-g,c[2]=g-(D-_)+(P-_),c[3]=D;let J=function(t,e){let n=e[0];for(let r=1;r<t;r++)n+=e[r];return n}(4,c),K=i*h;if(J>=K||-J>=K)return J;if(M=t-(E+(_=t-E))+(_-b),j=f-(G+(_=f-G))+(_-b),x=o-(H+(_=o-H))+(_-y),m=p-(I+(_=p-I))+(_-y),0===M&&0===x&&0===j&&0===m)return J;if(K=u*h+n*Math.abs(J),(J+=E*m+I*M-(H*j+G*x))>=K||-J>=K)return J;_=(z=(A=M-(w=(v=e*M)-(v-M)))*(O=I-(F=(v=e*I)-(v-I)))-((q=M*I)-w*F-A*F-w*O))-(P=z-(C=(A=x-(w=(v=e*x)-(v-x)))*(O=G-(F=(v=e*G)-(v-G)))-((B=x*G)-w*F-A*F-w*O))),l[0]=z-(P+_)+(_-C),_=(k=q-((g=q+P)-(_=g-q))+(P-_))-(P=k-B),l[1]=k-(P+_)+(_-B),_=(D=g+P)-g,l[2]=g-(D-_)+(P-_),l[3]=D;const L=r(4,c,4,l,s);_=(z=(A=E-(w=(v=e*E)-(v-E)))*(O=m-(F=(v=e*m)-(v-m)))-((q=E*m)-w*F-A*F-w*O))-(P=z-(C=(A=H-(w=(v=e*H)-(v-H)))*(O=j-(F=(v=e*j)-(v-j)))-((B=H*j)-w*F-A*F-w*O))),l[0]=z-(P+_)+(_-C),_=(k=q-((g=q+P)-(_=g-q))+(P-_))-(P=k-B),l[1]=k-(P+_)+(_-B),_=(D=g+P)-g,l[2]=g-(D-_)+(P-_),l[3]=D;const N=r(L,s,4,l,a);_=(z=(A=M-(w=(v=e*M)-(v-M)))*(O=m-(F=(v=e*m)-(v-m)))-((q=M*m)-w*F-A*F-w*O))-(P=z-(C=(A=x-(w=(v=e*x)-(v-x)))*(O=j-(F=(v=e*j)-(v-j)))-((B=x*j)-w*F-A*F-w*O))),l[0]=z-(P+_)+(_-C),_=(k=q-((g=q+P)-(_=g-q))+(P-_))-(P=k-B),l[1]=k-(P+_)+(_-B),_=(D=g+P)-g,l[2]=g-(D-_)+(P-_),l[3]=D;const Q=r(N,a,4,l,d);return d[Q-1]}(t,o,p,b,y,h,m)},t.orient2dfast=function(t,e,n,r,o,f){return(e-f)*(n-o)-(t-o)*(r-f)},Object.defineProperty(t,\"__esModule\",{value:!0})});\n", "'use strict';\n\nvar RBush = require('rbush');\nvar Queue = require('tinyqueue');\nvar pointInPolygon = require('point-in-polygon');\nvar orient = require('robust-predicates/umd/orient2d.min.js').orient2d;\n\n// Fix for require issue in webpack https://github.com/mapbox/concaveman/issues/18\nif (Queue.default) {\n    Queue = Queue.default;\n}\n\nmodule.exports = concaveman;\nmodule.exports.default = concaveman;\n\nfunction concaveman(points, concavity, lengthThreshold) {\n    // a relative measure of concavity; higher value means simpler hull\n    concavity = Math.max(0, concavity === undefined ? 2 : concavity);\n\n    // when a segment goes below this length threshold, it won't be drilled down further\n    lengthThreshold = lengthThreshold || 0;\n\n    // start with a convex hull of the points\n    var hull = fastConvexHull(points);\n\n    // index the points with an R-tree\n    var tree = new RBush(16);\n    tree.toBBox = function (a) {\n        return {\n            minX: a[0],\n            minY: a[1],\n            maxX: a[0],\n            maxY: a[1]\n        };\n    };\n    tree.compareMinX = function (a, b) { return a[0] - b[0]; };\n    tree.compareMinY = function (a, b) { return a[1] - b[1]; };\n\n    tree.load(points);\n\n    // turn the convex hull into a linked list and populate the initial edge queue with the nodes\n    var queue = [];\n    for (var i = 0, last; i < hull.length; i++) {\n        var p = hull[i];\n        tree.remove(p);\n        last = insertNode(p, last);\n        queue.push(last);\n    }\n\n    // index the segments with an R-tree (for intersection checks)\n    var segTree = new RBush(16);\n    for (i = 0; i < queue.length; i++) segTree.insert(updateBBox(queue[i]));\n\n    var sqConcavity = concavity * concavity;\n    var sqLenThreshold = lengthThreshold * lengthThreshold;\n\n    // process edges one by one\n    while (queue.length) {\n        var node = queue.shift();\n        var a = node.p;\n        var b = node.next.p;\n\n        // skip the edge if it's already short enough\n        var sqLen = getSqDist(a, b);\n        if (sqLen < sqLenThreshold) continue;\n\n        var maxSqLen = sqLen / sqConcavity;\n\n        // find the best connection point for the current edge to flex inward to\n        p = findCandidate(tree, node.prev.p, a, b, node.next.next.p, maxSqLen, segTree);\n\n        // if we found a connection and it satisfies our concavity measure\n        if (p && Math.min(getSqDist(p, a), getSqDist(p, b)) <= maxSqLen) {\n            // connect the edge endpoints through this point and add 2 new edges to the queue\n            queue.push(node);\n            queue.push(insertNode(p, node));\n\n            // update point and segment indexes\n            tree.remove(p);\n            segTree.remove(node);\n            segTree.insert(updateBBox(node));\n            segTree.insert(updateBBox(node.next));\n        }\n    }\n\n    // convert the resulting hull linked list to an array of points\n    node = last;\n    var concave = [];\n    do {\n        concave.push(node.p);\n        node = node.next;\n    } while (node !== last);\n\n    concave.push(node.p);\n\n    return concave;\n}\n\nfunction findCandidate(tree, a, b, c, d, maxDist, segTree) {\n    var queue = new Queue([], compareDist);\n    var node = tree.data;\n\n    // search through the point R-tree with a depth-first search using a priority queue\n    // in the order of distance to the edge (b, c)\n    while (node) {\n        for (var i = 0; i < node.children.length; i++) {\n            var child = node.children[i];\n\n            var dist = node.leaf ? sqSegDist(child, b, c) : sqSegBoxDist(b, c, child);\n            if (dist > maxDist) continue; // skip the node if it's farther than we ever need\n\n            queue.push({\n                node: child,\n                dist: dist\n            });\n        }\n\n        while (queue.length && !queue.peek().node.children) {\n            var item = queue.pop();\n            var p = item.node;\n\n            // skip all points that are as close to adjacent edges (a,b) and (c,d),\n            // and points that would introduce self-intersections when connected\n            var d0 = sqSegDist(p, a, b);\n            var d1 = sqSegDist(p, c, d);\n            if (item.dist < d0 && item.dist < d1 &&\n                noIntersections(b, p, segTree) &&\n                noIntersections(c, p, segTree)) return p;\n        }\n\n        node = queue.pop();\n        if (node) node = node.node;\n    }\n\n    return null;\n}\n\nfunction compareDist(a, b) {\n    return a.dist - b.dist;\n}\n\n// square distance from a segment bounding box to the given one\nfunction sqSegBoxDist(a, b, bbox) {\n    if (inside(a, bbox) || inside(b, bbox)) return 0;\n    var d1 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.minX, bbox.minY, bbox.maxX, bbox.minY);\n    if (d1 === 0) return 0;\n    var d2 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.minX, bbox.minY, bbox.minX, bbox.maxY);\n    if (d2 === 0) return 0;\n    var d3 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.maxX, bbox.minY, bbox.maxX, bbox.maxY);\n    if (d3 === 0) return 0;\n    var d4 = sqSegSegDist(a[0], a[1], b[0], b[1], bbox.minX, bbox.maxY, bbox.maxX, bbox.maxY);\n    if (d4 === 0) return 0;\n    return Math.min(d1, d2, d3, d4);\n}\n\nfunction inside(a, bbox) {\n    return a[0] >= bbox.minX &&\n           a[0] <= bbox.maxX &&\n           a[1] >= bbox.minY &&\n           a[1] <= bbox.maxY;\n}\n\n// check if the edge (a,b) doesn't intersect any other edges\nfunction noIntersections(a, b, segTree) {\n    var minX = Math.min(a[0], b[0]);\n    var minY = Math.min(a[1], b[1]);\n    var maxX = Math.max(a[0], b[0]);\n    var maxY = Math.max(a[1], b[1]);\n\n    var edges = segTree.search({minX: minX, minY: minY, maxX: maxX, maxY: maxY});\n    for (var i = 0; i < edges.length; i++) {\n        if (intersects(edges[i].p, edges[i].next.p, a, b)) return false;\n    }\n    return true;\n}\n\nfunction cross(p1, p2, p3) {\n    return orient(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\n// check if the edges (p1,q1) and (p2,q2) intersect\nfunction intersects(p1, q1, p2, q2) {\n    return p1 !== q2 && q1 !== p2 &&\n        cross(p1, q1, p2) > 0 !== cross(p1, q1, q2) > 0 &&\n        cross(p2, q2, p1) > 0 !== cross(p2, q2, q1) > 0;\n}\n\n// update the bounding box of a node's edge\nfunction updateBBox(node) {\n    var p1 = node.p;\n    var p2 = node.next.p;\n    node.minX = Math.min(p1[0], p2[0]);\n    node.minY = Math.min(p1[1], p2[1]);\n    node.maxX = Math.max(p1[0], p2[0]);\n    node.maxY = Math.max(p1[1], p2[1]);\n    return node;\n}\n\n// speed up convex hull by filtering out points inside quadrilateral formed by 4 extreme points\nfunction fastConvexHull(points) {\n    var left = points[0];\n    var top = points[0];\n    var right = points[0];\n    var bottom = points[0];\n\n    // find the leftmost, rightmost, topmost and bottommost points\n    for (var i = 0; i < points.length; i++) {\n        var p = points[i];\n        if (p[0] < left[0]) left = p;\n        if (p[0] > right[0]) right = p;\n        if (p[1] < top[1]) top = p;\n        if (p[1] > bottom[1]) bottom = p;\n    }\n\n    // filter out points that are inside the resulting quadrilateral\n    var cull = [left, top, right, bottom];\n    var filtered = cull.slice();\n    for (i = 0; i < points.length; i++) {\n        if (!pointInPolygon(points[i], cull)) filtered.push(points[i]);\n    }\n\n    // get convex hull around the filtered points\n    return convexHull(filtered);\n}\n\n// create a new node in a doubly linked list\nfunction insertNode(p, prev) {\n    var node = {\n        p: p,\n        prev: null,\n        next: null,\n        minX: 0,\n        minY: 0,\n        maxX: 0,\n        maxY: 0\n    };\n\n    if (!prev) {\n        node.prev = node;\n        node.next = node;\n\n    } else {\n        node.next = prev.next;\n        node.prev = prev;\n        prev.next.prev = node;\n        prev.next = node;\n    }\n    return node;\n}\n\n// square distance between 2 points\nfunction getSqDist(p1, p2) {\n\n    var dx = p1[0] - p2[0],\n        dy = p1[1] - p2[1];\n\n    return dx * dx + dy * dy;\n}\n\n// square distance from a point to a segment\nfunction sqSegDist(p, p1, p2) {\n\n    var x = p1[0],\n        y = p1[1],\n        dx = p2[0] - x,\n        dy = p2[1] - y;\n\n    if (dx !== 0 || dy !== 0) {\n\n        var t = ((p[0] - x) * dx + (p[1] - y) * dy) / (dx * dx + dy * dy);\n\n        if (t > 1) {\n            x = p2[0];\n            y = p2[1];\n\n        } else if (t > 0) {\n            x += dx * t;\n            y += dy * t;\n        }\n    }\n\n    dx = p[0] - x;\n    dy = p[1] - y;\n\n    return dx * dx + dy * dy;\n}\n\n// segment to segment distance, ported from http://geomalgorithms.com/a07-_distance.html by Dan Sunday\nfunction sqSegSegDist(x0, y0, x1, y1, x2, y2, x3, y3) {\n    var ux = x1 - x0;\n    var uy = y1 - y0;\n    var vx = x3 - x2;\n    var vy = y3 - y2;\n    var wx = x0 - x2;\n    var wy = y0 - y2;\n    var a = ux * ux + uy * uy;\n    var b = ux * vx + uy * vy;\n    var c = vx * vx + vy * vy;\n    var d = ux * wx + uy * wy;\n    var e = vx * wx + vy * wy;\n    var D = a * c - b * b;\n\n    var sc, sN, tc, tN;\n    var sD = D;\n    var tD = D;\n\n    if (D === 0) {\n        sN = 0;\n        sD = 1;\n        tN = e;\n        tD = c;\n    } else {\n        sN = b * e - c * d;\n        tN = a * e - b * d;\n        if (sN < 0) {\n            sN = 0;\n            tN = e;\n            tD = c;\n        } else if (sN > sD) {\n            sN = sD;\n            tN = e + b;\n            tD = c;\n        }\n    }\n\n    if (tN < 0.0) {\n        tN = 0.0;\n        if (-d < 0.0) sN = 0.0;\n        else if (-d > a) sN = sD;\n        else {\n            sN = -d;\n            sD = a;\n        }\n    } else if (tN > tD) {\n        tN = tD;\n        if ((-d + b) < 0.0) sN = 0;\n        else if (-d + b > a) sN = sD;\n        else {\n            sN = -d + b;\n            sD = a;\n        }\n    }\n\n    sc = sN === 0 ? 0 : sN / sD;\n    tc = tN === 0 ? 0 : tN / tD;\n\n    var cx = (1 - sc) * x0 + sc * x1;\n    var cy = (1 - sc) * y0 + sc * y1;\n    var cx2 = (1 - tc) * x2 + tc * x3;\n    var cy2 = (1 - tc) * y2 + tc * y3;\n    var dx = cx2 - cx;\n    var dy = cy2 - cy;\n\n    return dx * dx + dy * dy;\n}\n\nfunction compareByX(a, b) {\n    return a[0] === b[0] ? a[1] - b[1] : a[0] - b[0];\n}\n\nfunction convexHull(points) {\n    points.sort(compareByX);\n\n    var lower = [];\n    for (var i = 0; i < points.length; i++) {\n        while (lower.length >= 2 && cross(lower[lower.length - 2], lower[lower.length - 1], points[i]) <= 0) {\n            lower.pop();\n        }\n        lower.push(points[i]);\n    }\n\n    var upper = [];\n    for (var ii = points.length - 1; ii >= 0; ii--) {\n        while (upper.length >= 2 && cross(upper[upper.length - 2], upper[upper.length - 1], points[ii]) <= 0) {\n            upper.pop();\n        }\n        upper.push(points[ii]);\n    }\n\n    upper.pop();\n    lower.pop();\n    return lower.concat(upper);\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,KAAG,MAAM,QAAM,EAAE;AAAA,IAAC,EAAE,SAAK,WAAU;AAAC;AAAa,eAAS,EAAEA,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,SAAC,SAASJ,GAAEK,IAAEJ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,iBAAKD,KAAED,MAAG;AAAC,gBAAGC,KAAED,KAAE,KAAI;AAAC,kBAAII,KAAEH,KAAED,KAAE,GAAEK,KAAEN,KAAEC,KAAE,GAAEM,KAAE,KAAK,IAAIF,EAAC,GAAEG,KAAE,MAAG,KAAK,IAAI,IAAED,KAAE,CAAC,GAAEE,KAAE,MAAG,KAAK,KAAKF,KAAEC,MAAGH,KAAEG,MAAGH,EAAC,KAAGC,KAAED,KAAE,IAAE,IAAE,KAAG,IAAGK,KAAE,KAAK,IAAIT,IAAE,KAAK,MAAMD,KAAEM,KAAEE,KAAEH,KAAEI,EAAC,CAAC,GAAEE,KAAE,KAAK,IAAIT,IAAE,KAAK,MAAMF,MAAGK,KAAEC,MAAGE,KAAEH,KAAEI,EAAC,CAAC;AAAE,cAAAV,GAAEK,IAAEJ,IAAEU,IAAEC,IAAER,EAAC;AAAA,YAAC;AAAC,gBAAIS,KAAER,GAAEJ,EAAC,GAAEa,KAAEZ,IAAE,IAAEC;AAAE,iBAAI,EAAEE,IAAEH,IAAED,EAAC,GAAEG,GAAEC,GAAEF,EAAC,GAAEU,EAAC,IAAE,KAAG,EAAER,IAAEH,IAAEC,EAAC,GAAEW,KAAE,KAAG;AAAC,mBAAI,EAAET,IAAES,IAAE,CAAC,GAAEA,MAAI,KAAIV,GAAEC,GAAES,EAAC,GAAED,EAAC,IAAE,IAAG,CAAAC;AAAI,qBAAKV,GAAEC,GAAE,CAAC,GAAEQ,EAAC,IAAE,IAAG;AAAA,YAAG;AAAC,kBAAIT,GAAEC,GAAEH,EAAC,GAAEW,EAAC,IAAE,EAAER,IAAEH,IAAE,CAAC,IAAE,EAAEG,IAAE,EAAE,GAAEF,EAAC,GAAE,KAAGF,OAAIC,KAAE,IAAE,IAAGD,MAAG,MAAIE,KAAE,IAAE;AAAA,UAAE;AAAA,QAAC,EAAEH,IAAEC,IAAEC,MAAG,GAAEC,MAAGH,GAAE,SAAO,GAAEI,MAAG,CAAC;AAAA,MAAC;AAAC,eAAS,EAAEJ,IAAEe,IAAEV,IAAE;AAAC,YAAIJ,KAAED,GAAEe,EAAC;AAAE,QAAAf,GAAEe,EAAC,IAAEf,GAAEK,EAAC,GAAEL,GAAEK,EAAC,IAAEJ;AAAA,MAAC;AAAC,eAAS,EAAED,IAAEe,IAAE;AAAC,eAAOf,KAAEe,KAAE,KAAGf,KAAEe,KAAE,IAAE;AAAA,MAAC;AAAC,UAAI,IAAE,SAASf,IAAE;AAAC,mBAASA,OAAIA,KAAE,IAAG,KAAK,cAAY,KAAK,IAAI,GAAEA,EAAC,GAAE,KAAK,cAAY,KAAK,IAAI,GAAE,KAAK,KAAK,MAAG,KAAK,WAAW,CAAC,GAAE,KAAK,MAAM;AAAA,MAAC;AAAE,eAAS,EAAEA,IAAEe,IAAEV,IAAE;AAAC,YAAG,CAACA,GAAE,QAAOU,GAAE,QAAQf,EAAC;AAAE,iBAAQC,KAAE,GAAEA,KAAEc,GAAE,QAAOd,KAAI,KAAGI,GAAEL,IAAEe,GAAEd,EAAC,CAAC,EAAE,QAAOA;AAAE,eAAM;AAAA,MAAE;AAAC,eAAS,EAAED,IAAEe,IAAE;AAAC,UAAEf,IAAE,GAAEA,GAAE,SAAS,QAAOe,IAAEf,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEe,IAAEV,IAAEJ,IAAEC,IAAE;AAAC,QAAAA,OAAIA,KAAE,EAAE,IAAI,IAAGA,GAAE,OAAK,IAAE,GAAEA,GAAE,OAAK,IAAE,GAAEA,GAAE,OAAK,KAAG,GAAEA,GAAE,OAAK,KAAG;AAAE,iBAAQC,KAAEY,IAAEZ,KAAEE,IAAEF,MAAI;AAAC,cAAIC,KAAEJ,GAAE,SAASG,EAAC;AAAE,YAAED,IAAEF,GAAE,OAAKC,GAAEG,EAAC,IAAEA,EAAC;AAAA,QAAC;AAAC,eAAOF;AAAA,MAAC;AAAC,eAAS,EAAEF,IAAEe,IAAE;AAAC,eAAOf,GAAE,OAAK,KAAK,IAAIA,GAAE,MAAKe,GAAE,IAAI,GAAEf,GAAE,OAAK,KAAK,IAAIA,GAAE,MAAKe,GAAE,IAAI,GAAEf,GAAE,OAAK,KAAK,IAAIA,GAAE,MAAKe,GAAE,IAAI,GAAEf,GAAE,OAAK,KAAK,IAAIA,GAAE,MAAKe,GAAE,IAAI,GAAEf;AAAA,MAAC;AAAC,eAAS,EAAEA,IAAEe,IAAE;AAAC,eAAOf,GAAE,OAAKe,GAAE;AAAA,MAAI;AAAC,eAAS,EAAEf,IAAEe,IAAE;AAAC,eAAOf,GAAE,OAAKe,GAAE;AAAA,MAAI;AAAC,eAAS,EAAEf,IAAE;AAAC,gBAAOA,GAAE,OAAKA,GAAE,SAAOA,GAAE,OAAKA,GAAE;AAAA,MAAK;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAOA,GAAE,OAAKA,GAAE,QAAMA,GAAE,OAAKA,GAAE;AAAA,MAAK;AAAC,eAAS,EAAEA,IAAEe,IAAE;AAAC,eAAOf,GAAE,QAAMe,GAAE,QAAMf,GAAE,QAAMe,GAAE,QAAMA,GAAE,QAAMf,GAAE,QAAMe,GAAE,QAAMf,GAAE;AAAA,MAAI;AAAC,eAAS,EAAEA,IAAEe,IAAE;AAAC,eAAOA,GAAE,QAAMf,GAAE,QAAMe,GAAE,QAAMf,GAAE,QAAMe,GAAE,QAAMf,GAAE,QAAMe,GAAE,QAAMf,GAAE;AAAA,MAAI;AAAC,eAAS,EAAEA,IAAE;AAAC,eAAM,EAAC,UAASA,IAAE,QAAO,GAAE,MAAK,MAAG,MAAK,IAAE,GAAE,MAAK,IAAE,GAAE,MAAK,KAAG,GAAE,MAAK,KAAG,EAAC;AAAA,MAAC;AAAC,eAAS,EAAEe,IAAEV,IAAEJ,IAAEC,IAAEC,IAAE;AAAC,iBAAQC,KAAE,CAACC,IAAEJ,EAAC,GAAEG,GAAE,SAAQ,KAAG,GAAGH,KAAEG,GAAE,IAAI,MAAIC,KAAED,GAAE,IAAI,MAAIF,KAAG;AAAC,cAAII,KAAED,KAAE,KAAK,MAAMJ,KAAEI,MAAGH,KAAE,CAAC,IAAEA;AAAE,YAAEa,IAAET,IAAED,IAAEJ,IAAEE,EAAC,GAAEC,GAAE,KAAKC,IAAEC,IAAEA,IAAEL,EAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO,EAAE,UAAU,MAAI,WAAU;AAAC,eAAO,KAAK,KAAK,KAAK,MAAK,CAAC,CAAC;AAAA,MAAC,GAAE,EAAE,UAAU,SAAO,SAASD,IAAE;AAAC,YAAIe,KAAE,KAAK,MAAKV,KAAE,CAAC;AAAE,YAAG,CAAC,EAAEL,IAAEe,EAAC,EAAE,QAAOV;AAAE,iBAAQJ,KAAE,KAAK,QAAOC,KAAE,CAAC,GAAEa,MAAG;AAAC,mBAAQZ,KAAE,GAAEA,KAAEY,GAAE,SAAS,QAAOZ,MAAI;AAAC,gBAAIC,KAAEW,GAAE,SAASZ,EAAC,GAAEG,KAAES,GAAE,OAAKd,GAAEG,EAAC,IAAEA;AAAE,cAAEJ,IAAEM,EAAC,MAAIS,GAAE,OAAKV,GAAE,KAAKD,EAAC,IAAE,EAAEJ,IAAEM,EAAC,IAAE,KAAK,KAAKF,IAAEC,EAAC,IAAEH,GAAE,KAAKE,EAAC;AAAA,UAAE;AAAC,UAAAW,KAAEb,GAAE,IAAI;AAAA,QAAC;AAAC,eAAOG;AAAA,MAAC,GAAE,EAAE,UAAU,WAAS,SAASL,IAAE;AAAC,YAAIe,KAAE,KAAK;AAAK,YAAG,CAAC,EAAEf,IAAEe,EAAC,EAAE,QAAM;AAAG,iBAAQV,KAAE,CAAC,GAAEU,MAAG;AAAC,mBAAQd,KAAE,GAAEA,KAAEc,GAAE,SAAS,QAAOd,MAAI;AAAC,gBAAIC,KAAEa,GAAE,SAASd,EAAC,GAAEE,KAAEY,GAAE,OAAK,KAAK,OAAOb,EAAC,IAAEA;AAAE,gBAAG,EAAEF,IAAEG,EAAC,GAAE;AAAC,kBAAGY,GAAE,QAAM,EAAEf,IAAEG,EAAC,EAAE,QAAM;AAAG,cAAAE,GAAE,KAAKH,EAAC;AAAA,YAAC;AAAA,UAAC;AAAC,UAAAa,KAAEV,GAAE,IAAI;AAAA,QAAC;AAAC,eAAM;AAAA,MAAE,GAAE,EAAE,UAAU,OAAK,SAASL,IAAE;AAAC,YAAG,CAACA,MAAG,CAACA,GAAE,OAAO,QAAO;AAAK,YAAGA,GAAE,SAAO,KAAK,aAAY;AAAC,mBAAQe,KAAE,GAAEA,KAAEf,GAAE,QAAOe,KAAI,MAAK,OAAOf,GAAEe,EAAC,CAAC;AAAE,iBAAO;AAAA,QAAI;AAAC,YAAIV,KAAE,KAAK,OAAOL,GAAE,MAAM,GAAE,GAAEA,GAAE,SAAO,GAAE,CAAC;AAAE,YAAG,KAAK,KAAK,SAAS,OAAO,KAAG,KAAK,KAAK,WAASK,GAAE,OAAO,MAAK,WAAW,KAAK,MAAKA,EAAC;AAAA,aAAM;AAAC,cAAG,KAAK,KAAK,SAAOA,GAAE,QAAO;AAAC,gBAAIJ,KAAE,KAAK;AAAK,iBAAK,OAAKI,IAAEA,KAAEJ;AAAA,UAAC;AAAC,eAAK,QAAQI,IAAE,KAAK,KAAK,SAAOA,GAAE,SAAO,GAAE,IAAE;AAAA,QAAC;AAAA,YAAM,MAAK,OAAKA;AAAE,eAAO;AAAA,MAAI,GAAE,EAAE,UAAU,SAAO,SAASL,IAAE;AAAC,eAAOA,MAAG,KAAK,QAAQA,IAAE,KAAK,KAAK,SAAO,CAAC,GAAE;AAAA,MAAI,GAAE,EAAE,UAAU,QAAM,WAAU;AAAC,eAAO,KAAK,OAAK,EAAE,CAAC,CAAC,GAAE;AAAA,MAAI,GAAE,EAAE,UAAU,SAAO,SAASA,IAAEe,IAAE;AAAC,YAAG,CAACf,GAAE,QAAO;AAAK,iBAAQK,IAAEJ,IAAEE,IAAEC,KAAE,KAAK,MAAKE,KAAE,KAAK,OAAON,EAAC,GAAEO,KAAE,CAAC,GAAEC,KAAE,CAAC,GAAEJ,MAAGG,GAAE,UAAQ;AAAC,cAAGH,OAAIA,KAAEG,GAAE,IAAI,GAAEN,KAAEM,GAAEA,GAAE,SAAO,CAAC,GAAEF,KAAEG,GAAE,IAAI,GAAEL,KAAE,OAAIC,GAAE,MAAK;AAAC,gBAAIK,KAAE,EAAET,IAAEI,GAAE,UAASW,EAAC;AAAE,gBAAG,OAAKN,GAAE,QAAOL,GAAE,SAAS,OAAOK,IAAE,CAAC,GAAEF,GAAE,KAAKH,EAAC,GAAE,KAAK,UAAUG,EAAC,GAAE;AAAA,UAAI;AAAC,UAAAJ,MAAGC,GAAE,QAAM,CAAC,EAAEA,IAAEE,EAAC,IAAEL,MAAGI,MAAID,KAAEH,GAAE,SAASI,EAAC,GAAEF,KAAE,SAAIC,KAAE,QAAMG,GAAE,KAAKH,EAAC,GAAEI,GAAE,KAAKH,EAAC,GAAEA,KAAE,GAAEJ,KAAEG,IAAEA,KAAEA,GAAE,SAAS,CAAC;AAAA,QAAE;AAAC,eAAO;AAAA,MAAI,GAAE,EAAE,UAAU,SAAO,SAASJ,IAAE;AAAC,eAAOA;AAAA,MAAC,GAAE,EAAE,UAAU,cAAY,SAASA,IAAEe,IAAE;AAAC,eAAOf,GAAE,OAAKe,GAAE;AAAA,MAAI,GAAE,EAAE,UAAU,cAAY,SAASf,IAAEe,IAAE;AAAC,eAAOf,GAAE,OAAKe,GAAE;AAAA,MAAI,GAAE,EAAE,UAAU,SAAO,WAAU;AAAC,eAAO,KAAK;AAAA,MAAI,GAAE,EAAE,UAAU,WAAS,SAASf,IAAE;AAAC,eAAO,KAAK,OAAKA,IAAE;AAAA,MAAI,GAAE,EAAE,UAAU,OAAK,SAASA,IAAEe,IAAE;AAAC,iBAAQV,KAAE,CAAC,GAAEL,KAAG,CAAAA,GAAE,OAAKe,GAAE,KAAK,MAAMA,IAAEf,GAAE,QAAQ,IAAEK,GAAE,KAAK,MAAMA,IAAEL,GAAE,QAAQ,GAAEA,KAAEK,GAAE,IAAI;AAAE,eAAOU;AAAA,MAAC,GAAE,EAAE,UAAU,SAAO,SAASf,IAAEe,IAAEV,IAAEJ,IAAE;AAAC,YAAIC,IAAEE,KAAEC,KAAEU,KAAE,GAAET,KAAE,KAAK;AAAY,YAAGF,MAAGE,GAAE,QAAO,EAAEJ,KAAE,EAAEF,GAAE,MAAMe,IAAEV,KAAE,CAAC,CAAC,GAAE,KAAK,MAAM,GAAEH;AAAE,QAAAD,OAAIA,KAAE,KAAK,KAAK,KAAK,IAAIG,EAAC,IAAE,KAAK,IAAIE,EAAC,CAAC,GAAEA,KAAE,KAAK,KAAKF,KAAE,KAAK,IAAIE,IAAEL,KAAE,CAAC,CAAC,KAAIC,KAAE,EAAE,CAAC,CAAC,GAAG,OAAK,OAAGA,GAAE,SAAOD;AAAE,YAAIM,KAAE,KAAK,KAAKH,KAAEE,EAAC,GAAEE,KAAED,KAAE,KAAK,KAAK,KAAK,KAAKD,EAAC,CAAC;AAAE,UAAEN,IAAEe,IAAEV,IAAEG,IAAE,KAAK,WAAW;AAAE,iBAAQC,KAAEM,IAAEN,MAAGJ,IAAEI,MAAGD,IAAE;AAAC,cAAIE,KAAE,KAAK,IAAID,KAAED,KAAE,GAAEH,EAAC;AAAE,YAAEL,IAAES,IAAEC,IAAEH,IAAE,KAAK,WAAW;AAAE,mBAAQI,KAAEF,IAAEE,MAAGD,IAAEC,MAAGJ,IAAE;AAAC,gBAAIK,KAAE,KAAK,IAAID,KAAEJ,KAAE,GAAEG,EAAC;AAAE,YAAAR,GAAE,SAAS,KAAK,KAAK,OAAOF,IAAEW,IAAEC,IAAEX,KAAE,CAAC,CAAC;AAAA,UAAC;AAAA,QAAC;AAAC,eAAO,EAAEC,IAAE,KAAK,MAAM,GAAEA;AAAA,MAAC,GAAE,EAAE,UAAU,iBAAe,SAASF,IAAEe,IAAEV,IAAEJ,IAAE;AAAC,eAAKA,GAAE,KAAKc,EAAC,GAAE,CAACA,GAAE,QAAMd,GAAE,SAAO,MAAII,MAAG;AAAC,mBAAQH,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,KAAE,QAAOE,KAAE,GAAEA,KAAES,GAAE,SAAS,QAAOT,MAAI;AAAC,gBAAIC,KAAEQ,GAAE,SAAST,EAAC,GAAEE,KAAE,EAAED,EAAC,GAAEG,MAAGC,KAAEX,IAAEY,KAAEL,KAAG,KAAK,IAAIK,GAAE,MAAKD,GAAE,IAAI,IAAE,KAAK,IAAIC,GAAE,MAAKD,GAAE,IAAI,MAAI,KAAK,IAAIC,GAAE,MAAKD,GAAE,IAAI,IAAE,KAAK,IAAIC,GAAE,MAAKD,GAAE,IAAI,KAAGH;AAAG,YAAAE,KAAEP,MAAGA,KAAEO,IAAER,KAAEM,KAAEN,KAAEM,KAAEN,IAAEE,KAAEG,MAAGG,OAAIP,MAAGK,KAAEN,OAAIA,KAAEM,IAAEJ,KAAEG;AAAA,UAAE;AAAC,UAAAQ,KAAEX,MAAGW,GAAE,SAAS,CAAC;AAAA,QAAC;AAAC,YAAIJ,IAAEC;AAAE,eAAOG;AAAA,MAAC,GAAE,EAAE,UAAU,UAAQ,SAASf,IAAEe,IAAEV,IAAE;AAAC,YAAIJ,KAAEI,KAAEL,KAAE,KAAK,OAAOA,EAAC,GAAEE,KAAE,CAAC,GAAEC,KAAE,KAAK,eAAeF,IAAE,KAAK,MAAKc,IAAEb,EAAC;AAAE,aAAIC,GAAE,SAAS,KAAKH,EAAC,GAAE,EAAEG,IAAEF,EAAC,GAAEc,MAAG,KAAGb,GAAEa,EAAC,EAAE,SAAS,SAAO,KAAK,cAAa,MAAK,OAAOb,IAAEa,EAAC,GAAEA;AAAI,aAAK,oBAAoBd,IAAEC,IAAEa,EAAC;AAAA,MAAC,GAAE,EAAE,UAAU,SAAO,SAASf,IAAEe,IAAE;AAAC,YAAIV,KAAEL,GAAEe,EAAC,GAAEd,KAAEI,GAAE,SAAS,QAAOH,KAAE,KAAK;AAAY,aAAK,iBAAiBG,IAAEH,IAAED,EAAC;AAAE,YAAIG,KAAE,KAAK,kBAAkBC,IAAEH,IAAED,EAAC,GAAEK,KAAE,EAAED,GAAE,SAAS,OAAOD,IAAEC,GAAE,SAAS,SAAOD,EAAC,CAAC;AAAE,QAAAE,GAAE,SAAOD,GAAE,QAAOC,GAAE,OAAKD,GAAE,MAAK,EAAEA,IAAE,KAAK,MAAM,GAAE,EAAEC,IAAE,KAAK,MAAM,GAAES,KAAEf,GAAEe,KAAE,CAAC,EAAE,SAAS,KAAKT,EAAC,IAAE,KAAK,WAAWD,IAAEC,EAAC;AAAA,MAAC,GAAE,EAAE,UAAU,aAAW,SAASN,IAAEe,IAAE;AAAC,aAAK,OAAK,EAAE,CAACf,IAAEe,EAAC,CAAC,GAAE,KAAK,KAAK,SAAOf,GAAE,SAAO,GAAE,KAAK,KAAK,OAAK,OAAG,EAAE,KAAK,MAAK,KAAK,MAAM;AAAA,MAAC,GAAE,EAAE,UAAU,oBAAkB,SAASA,IAAEe,IAAEV,IAAE;AAAC,iBAAQJ,IAAEC,IAAEC,IAAEG,IAAEC,IAAEC,IAAEE,IAAEC,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,KAAEE,IAAEF,MAAGR,KAAEU,IAAEF,MAAI;AAAC,cAAIC,KAAE,EAAEd,IAAE,GAAEa,IAAE,KAAK,MAAM,GAAE,IAAE,EAAEb,IAAEa,IAAER,IAAE,KAAK,MAAM,GAAE,KAAGH,KAAEY,IAAEX,KAAE,GAAEG,KAAE,QAAOC,KAAE,QAAOC,KAAE,QAAOE,KAAE,QAAOJ,KAAE,KAAK,IAAIJ,GAAE,MAAKC,GAAE,IAAI,GAAEI,KAAE,KAAK,IAAIL,GAAE,MAAKC,GAAE,IAAI,GAAEK,KAAE,KAAK,IAAIN,GAAE,MAAKC,GAAE,IAAI,GAAEO,KAAE,KAAK,IAAIR,GAAE,MAAKC,GAAE,IAAI,GAAE,KAAK,IAAI,GAAEK,KAAEF,EAAC,IAAE,KAAK,IAAI,GAAEI,KAAEH,EAAC,IAAG,IAAE,EAAEO,EAAC,IAAE,EAAE,CAAC;AAAE,cAAEH,MAAGA,KAAE,GAAEV,KAAEY,IAAED,KAAE,IAAEA,KAAE,IAAEA,MAAG,MAAID,MAAG,IAAEC,OAAIA,KAAE,GAAEX,KAAEY;AAAA,QAAE;AAAC,eAAOZ,MAAGI,KAAEU;AAAA,MAAC,GAAE,EAAE,UAAU,mBAAiB,SAASf,IAAEe,IAAEV,IAAE;AAAC,YAAIJ,KAAED,GAAE,OAAK,KAAK,cAAY,GAAEE,KAAEF,GAAE,OAAK,KAAK,cAAY;AAAE,aAAK,eAAeA,IAAEe,IAAEV,IAAEJ,EAAC,IAAE,KAAK,eAAeD,IAAEe,IAAEV,IAAEH,EAAC,KAAGF,GAAE,SAAS,KAAKC,EAAC;AAAA,MAAC,GAAE,EAAE,UAAU,iBAAe,SAASD,IAAEe,IAAEV,IAAEJ,IAAE;AAAC,QAAAD,GAAE,SAAS,KAAKC,EAAC;AAAE,iBAAQC,KAAE,KAAK,QAAOC,KAAE,EAAEH,IAAE,GAAEe,IAAEb,EAAC,GAAEK,KAAE,EAAEP,IAAEK,KAAEU,IAAEV,IAAEH,EAAC,GAAEM,KAAE,EAAEL,EAAC,IAAE,EAAEI,EAAC,GAAEE,KAAEM,IAAEN,KAAEJ,KAAEU,IAAEN,MAAI;AAAC,cAAIE,KAAEX,GAAE,SAASS,EAAC;AAAE,YAAEN,IAAEH,GAAE,OAAKE,GAAES,EAAC,IAAEA,EAAC,GAAEH,MAAG,EAAEL,EAAC;AAAA,QAAC;AAAC,iBAAQS,KAAEP,KAAEU,KAAE,GAAEH,MAAGG,IAAEH,MAAI;AAAC,cAAIC,KAAEb,GAAE,SAASY,EAAC;AAAE,YAAEL,IAAEP,GAAE,OAAKE,GAAEW,EAAC,IAAEA,EAAC,GAAEL,MAAG,EAAED,EAAC;AAAA,QAAC;AAAC,eAAOC;AAAA,MAAC,GAAE,EAAE,UAAU,sBAAoB,SAASR,IAAEe,IAAEV,IAAE;AAAC,iBAAQJ,KAAEI,IAAEJ,MAAG,GAAEA,KAAI,GAAEc,GAAEd,EAAC,GAAED,EAAC;AAAA,MAAC,GAAE,EAAE,UAAU,YAAU,SAASA,IAAE;AAAC,iBAAQe,KAAEf,GAAE,SAAO,GAAEK,KAAE,QAAOU,MAAG,GAAEA,KAAI,OAAIf,GAAEe,EAAC,EAAE,SAAS,SAAOA,KAAE,KAAGV,KAAEL,GAAEe,KAAE,CAAC,EAAE,UAAU,OAAOV,GAAE,QAAQL,GAAEe,EAAC,CAAC,GAAE,CAAC,IAAE,KAAK,MAAM,IAAE,EAAEf,GAAEe,EAAC,GAAE,KAAK,MAAM;AAAA,MAAC,GAAE;AAAA,IAAC,CAAC;AAAA;AAAA;;;ACA7zM;AAAA;AAAA;AAAA;AA4EA,SAAS,eAAe,GAAG,GAAG;AAC1B,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI;AACpC;AA9EA,IACqB;AADrB;AAAA;AACA,IAAqB,YAArB,MAA+B;AAAA,MAC3B,YAAY,OAAO,CAAC,GAAG,UAAU,gBAAgB;AAC7C,aAAK,OAAO;AACZ,aAAK,SAAS,KAAK,KAAK;AACxB,aAAK,UAAU;AAEf,YAAI,KAAK,SAAS,GAAG;AACjB,mBAAS,KAAK,KAAK,UAAU,KAAK,GAAG,KAAK,GAAG,IAAK,MAAK,MAAM,CAAC;AAAA,QAClE;AAAA,MACJ;AAAA,MAEA,KAAK,MAAM;AACP,aAAK,KAAK,KAAK,IAAI;AACnB,aAAK;AACL,aAAK,IAAI,KAAK,SAAS,CAAC;AAAA,MAC5B;AAAA,MAEA,MAAM;AACF,YAAI,KAAK,WAAW,EAAG,QAAO;AAE9B,cAAM,MAAM,KAAK,KAAK,CAAC;AACvB,cAAM,SAAS,KAAK,KAAK,IAAI;AAC7B,aAAK;AAEL,YAAI,KAAK,SAAS,GAAG;AACjB,eAAK,KAAK,CAAC,IAAI;AACf,eAAK,MAAM,CAAC;AAAA,QAChB;AAEA,eAAO;AAAA,MACX;AAAA,MAEA,OAAO;AACH,eAAO,KAAK,KAAK,CAAC;AAAA,MACtB;AAAA,MAEA,IAAI,KAAK;AACL,cAAM,EAAC,MAAM,QAAO,IAAI;AACxB,cAAM,OAAO,KAAK,GAAG;AAErB,eAAO,MAAM,GAAG;AACZ,gBAAM,SAAU,MAAM,KAAM;AAC5B,gBAAM,UAAU,KAAK,MAAM;AAC3B,cAAI,QAAQ,MAAM,OAAO,KAAK,EAAG;AACjC,eAAK,GAAG,IAAI;AACZ,gBAAM;AAAA,QACV;AAEA,aAAK,GAAG,IAAI;AAAA,MAChB;AAAA,MAEA,MAAM,KAAK;AACP,cAAM,EAAC,MAAM,QAAO,IAAI;AACxB,cAAM,aAAa,KAAK,UAAU;AAClC,cAAM,OAAO,KAAK,GAAG;AAErB,eAAO,MAAM,YAAY;AACrB,cAAI,QAAQ,OAAO,KAAK;AACxB,cAAI,OAAO,KAAK,IAAI;AACpB,gBAAM,QAAQ,OAAO;AAErB,cAAI,QAAQ,KAAK,UAAU,QAAQ,KAAK,KAAK,GAAG,IAAI,IAAI,GAAG;AACvD,mBAAO;AACP,mBAAO,KAAK,KAAK;AAAA,UACrB;AACA,cAAI,QAAQ,MAAM,IAAI,KAAK,EAAG;AAE9B,eAAK,GAAG,IAAI;AACZ,gBAAM;AAAA,QACV;AAEA,aAAK,GAAG,IAAI;AAAA,MAChB;AAAA,IACJ;AAAA;AAAA;;;AC1EA;AAAA;AAAA,WAAO,UAAU,SAAS,mBAAoB,OAAO,IAAI,OAAO,KAAK;AACjE,UAAI,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AAC7B,UAAI,SAAS;AACb,UAAI,UAAU,OAAW,SAAQ;AACjC,UAAI,QAAQ,OAAW,OAAM,GAAG;AAChC,UAAI,OAAO,MAAI,SAAO;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC,GAAG,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC;AAC7C,YAAI,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC,GAAG,KAAK,GAAG,QAAM,IAAE,IAAE,CAAC;AAC7C,YAAI,YAAc,KAAK,MAAQ,KAAK,KAC5B,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AAC/C,YAAI,UAAW,UAAS,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACdA;AAAA;AAGA,WAAO,UAAU,SAAS,qBAAsB,OAAO,IAAI,OAAO,KAAK;AACnE,UAAI,IAAI,MAAM,CAAC,GAAG,IAAI,MAAM,CAAC;AAC7B,UAAI,SAAS;AACb,UAAI,UAAU,OAAW,SAAQ;AACjC,UAAI,QAAQ,OAAW,OAAM,GAAG;AAChC,UAAI,MAAM,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,MAAM,GAAG,IAAI,KAAK,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC;AAC3C,YAAI,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,IAAE,KAAK,EAAE,CAAC;AAC3C,YAAI,YAAc,KAAK,MAAQ,KAAK,KAC5B,KAAK,KAAK,OAAO,IAAI,OAAO,KAAK,MAAM;AAC/C,YAAI,UAAW,UAAS,CAAC;AAAA,MAC7B;AACA,aAAO;AAAA,IACX;AAAA;AAAA;;;ACjBA;AAAA;AAAA,QAAI,qBAAqB;AACzB,QAAI,uBAAuB;AAE3B,WAAO,UAAU,SAAS,eAAgB,OAAO,IAAI,OAAO,KAAK;AAC7D,UAAI,GAAG,SAAS,KAAK,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG;AACvC,eAAO,qBAAqB,OAAO,IAAI,OAAO,GAAG;AAAA,MACrD,OAAO;AACH,eAAO,mBAAmB,OAAO,IAAI,OAAO,GAAG;AAAA,MACnD;AAAA,IACJ;AACA,WAAO,QAAQ,SAAS;AACxB,WAAO,QAAQ,OAAO;AAAA;AAAA;;;ACXtB;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,EAAE,OAAO,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,SAAS,GAAE,CAAC,IAAE,GAAG,IAAE,KAAG,MAAM,aAAW,CAAC,CAAC;AAAA,IAAC,EAAE,SAAK,SAAS,GAAE;AAAC;AAAa,YAAM,IAAE,WAAU,IAAE;AAAsB,eAAS,EAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,YAAIC,IAAEC,IAAEC,IAAEC,IAAEC,KAAER,GAAE,CAAC,GAAES,KAAEP,GAAE,CAAC,GAAEQ,KAAE,GAAEC,KAAE;AAAE,QAAAF,KAAED,MAAGC,KAAE,CAACD,MAAGJ,KAAEI,IAAEA,KAAER,GAAE,EAAEU,EAAC,MAAIN,KAAEK,IAAEA,KAAEP,GAAE,EAAES,EAAC;AAAG,YAAI,IAAE;AAAE,YAAGD,KAAEX,MAAGY,KAAEV,GAAE,MAAIQ,KAAED,MAAGC,KAAE,CAACD,MAAGF,KAAEF,OAAIC,KAAEG,KAAEJ,MAAGI,KAAGA,KAAER,GAAE,EAAEU,EAAC,MAAIJ,KAAEF,OAAIC,KAAEI,KAAEL,MAAGK,KAAGA,KAAEP,GAAE,EAAES,EAAC,IAAGP,KAAEC,IAAE,MAAIC,OAAIH,GAAE,GAAG,IAAEG,KAAGI,KAAEX,MAAGY,KAAEV,KAAG,CAAAQ,KAAED,MAAGC,KAAE,CAACD,MAAGF,KAAEF,OAAIC,KAAED,KAAEI,OAAID,KAAEF,KAAED,QAAKI,KAAED,KAAGC,KAAER,GAAE,EAAEU,EAAC,MAAIJ,KAAEF,OAAIC,KAAED,KAAEK,OAAIF,KAAEF,KAAED,QAAKK,KAAEF,KAAGE,KAAEP,GAAE,EAAES,EAAC,IAAGP,KAAEC,IAAE,MAAIC,OAAIH,GAAE,GAAG,IAAEG;AAAG,eAAKI,KAAEX,KAAG,CAAAO,KAAEF,OAAIC,KAAED,KAAEI,OAAID,KAAEF,KAAED,QAAKI,KAAED,KAAGC,KAAER,GAAE,EAAEU,EAAC,GAAEN,KAAEC,IAAE,MAAIC,OAAIH,GAAE,GAAG,IAAEG;AAAG,eAAKK,KAAEV,KAAG,CAAAK,KAAEF,OAAIC,KAAED,KAAEK,OAAIF,KAAEF,KAAED,QAAKK,KAAEF,KAAGE,KAAEP,GAAE,EAAES,EAAC,GAAEP,KAAEC,IAAE,MAAIC,OAAIH,GAAE,GAAG,IAAEG;AAAG,eAAO,MAAIF,MAAG,MAAI,MAAID,GAAE,GAAG,IAAEC,KAAG;AAAA,MAAC;AAAC,eAAS,EAAEL,IAAE;AAAC,eAAO,IAAI,aAAaA,EAAC;AAAA,MAAC;AAAC,YAAM,IAAE,uBAAsB,IAAE,uBAAsB,IAAE,uBAAsB,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,CAAC,GAAE,IAAE,EAAE,EAAE,GAAE,IAAE,EAAE,EAAE,GAAE,IAAE,EAAE,CAAC;AAAE,QAAE,WAAS,SAASA,IAAEI,IAAE,GAAE,GAAE,GAAE,GAAE;AAAC,cAAM,KAAGA,KAAE,MAAI,IAAE,IAAG,KAAGJ,KAAE,MAAI,IAAE,IAAG,IAAE,IAAE;AAAE,YAAG,MAAI,KAAG,MAAI,KAAG,IAAE,KAAG,IAAE,EAAE,QAAO;AAAE,cAAM,IAAE,KAAK,IAAI,IAAE,CAAC;AAAE,eAAO,KAAK,IAAI,CAAC,KAAG,IAAE,IAAE,IAAE,CAAC,SAASA,IAAEI,IAAEC,IAAEQ,IAAEC,IAAEC,IAAEC,IAAE;AAAC,cAAIC,IAAEC,IAAEC,IAAEC,IAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAE,gBAAM,IAAEpB,KAAEc,IAAE,IAAET,KAAES,IAAE,IAAEV,KAAEW,IAAE,IAAEF,KAAEE;AAAE,eAAG,KAAG,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,QAAM,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,SAAO,IAAE,IAAE,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,OAAK,IAAE,KAAG,KAAG,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,QAAM,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,SAAO,IAAE,IAAE,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,MAAK,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,MAAI,IAAE,IAAE,MAAI,IAAE,IAAE,OAAK,IAAE,OAAK,IAAE,IAAE,IAAG,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,EAAE,CAAC,IAAE;AAAE,cAAI,IAAE,SAASf,IAAEC,IAAE;AAAC,gBAAIC,KAAED,GAAE,CAAC;AAAE,qBAAQE,KAAE,GAAEA,KAAEH,IAAEG,KAAI,CAAAD,MAAGD,GAAEE,EAAC;AAAE,mBAAOD;AAAA,UAAC,EAAE,GAAE,CAAC,GAAE,IAAE,IAAEc;AAAE,cAAG,KAAG,KAAG,CAAC,KAAG,EAAE,QAAO;AAAE,cAAGC,KAAEjB,MAAG,KAAG,IAAEA,KAAE,OAAK,IAAEc,KAAGK,KAAEd,MAAG,KAAG,IAAEA,KAAE,OAAK,IAAES,KAAGI,KAAEd,MAAG,KAAG,IAAEA,KAAE,OAAK,IAAEW,KAAGK,KAAEP,MAAG,KAAG,IAAEA,KAAE,OAAK,IAAEE,KAAG,MAAIE,MAAG,MAAIC,MAAG,MAAIC,MAAG,MAAIC,GAAE,QAAO;AAAE,cAAG,IAAE,IAAEJ,KAAE,IAAE,KAAK,IAAI,CAAC,IAAG,KAAG,IAAEI,KAAE,IAAEH,MAAG,IAAEE,KAAE,IAAED,QAAK,KAAG,CAAC,KAAG,EAAE,QAAO;AAAE,eAAG,KAAG,IAAED,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,SAAM,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,SAAO,IAAEA,KAAE,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,OAAK,IAAE,KAAG,KAAG,IAAEC,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,SAAM,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,SAAO,IAAEA,KAAE,KAAG,IAAE,IAAE,IAAE,IAAE,IAAE,MAAK,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,MAAI,IAAE,IAAE,MAAI,IAAE,IAAE,OAAK,IAAE,OAAK,IAAE,IAAE,IAAG,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,EAAE,CAAC,IAAE;AAAE,gBAAM,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,eAAG,KAAG,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,QAAM,IAAEE,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,UAAO,IAAE,IAAEA,MAAG,IAAE,IAAE,IAAE,IAAE,IAAE,OAAK,IAAE,KAAG,KAAG,IAAE,KAAG,KAAG,IAAE,IAAE,MAAI,IAAE,QAAM,IAAED,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,UAAO,IAAE,IAAEA,MAAG,IAAE,IAAE,IAAE,IAAE,IAAE,MAAK,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,MAAI,IAAE,IAAE,MAAI,IAAE,IAAE,OAAK,IAAE,OAAK,IAAE,IAAE,IAAG,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,EAAE,CAAC,IAAE;AAAE,gBAAM,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,eAAG,KAAG,IAAEF,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,SAAM,IAAEG,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,UAAO,IAAEH,KAAEG,MAAG,IAAE,IAAE,IAAE,IAAE,IAAE,OAAK,IAAE,KAAG,KAAG,IAAEF,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,SAAM,IAAEC,MAAG,KAAG,IAAE,IAAEA,OAAI,IAAEA,UAAO,IAAED,KAAEC,MAAG,IAAE,IAAE,IAAE,IAAE,IAAE,MAAK,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,MAAI,IAAE,IAAE,MAAI,IAAE,IAAE,OAAK,IAAE,OAAK,IAAE,IAAE,IAAG,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,KAAG,IAAE,IAAE,KAAG,GAAE,EAAE,CAAC,IAAE,KAAG,IAAE,MAAI,IAAE,IAAG,EAAE,CAAC,IAAE;AAAE,gBAAM,IAAE,EAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,iBAAO,EAAE,IAAE,CAAC;AAAA,QAAC,EAAEnB,IAAEI,IAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,MAAC,GAAE,EAAE,eAAa,SAASJ,IAAEC,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,gBAAOJ,KAAEI,OAAIH,KAAEE,OAAIJ,KAAEI,OAAID,KAAEE;AAAA,MAAE,GAAE,OAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC;AAAA,IAAC,CAAC;AAAA;AAAA;;;ACArrF;AAAA;AAEA,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,iBAAiB;AACrB,QAAI,SAAS,uBAAiD;AAG9D,QAAI,MAAM,SAAS;AACf,cAAQ,MAAM;AAAA,IAClB;AAEA,WAAO,UAAU;AACjB,WAAO,QAAQ,UAAU;AAEzB,aAAS,WAAW,QAAQ,WAAW,iBAAiB;AAEpD,kBAAY,KAAK,IAAI,GAAG,cAAc,SAAY,IAAI,SAAS;AAG/D,wBAAkB,mBAAmB;AAGrC,UAAI,OAAO,eAAe,MAAM;AAGhC,UAAI,OAAO,IAAI,MAAM,EAAE;AACvB,WAAK,SAAS,SAAUgB,IAAG;AACvB,eAAO;AAAA,UACH,MAAMA,GAAE,CAAC;AAAA,UACT,MAAMA,GAAE,CAAC;AAAA,UACT,MAAMA,GAAE,CAAC;AAAA,UACT,MAAMA,GAAE,CAAC;AAAA,QACb;AAAA,MACJ;AACA,WAAK,cAAc,SAAUA,IAAGC,IAAG;AAAE,eAAOD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,MAAG;AACzD,WAAK,cAAc,SAAUD,IAAGC,IAAG;AAAE,eAAOD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,MAAG;AAEzD,WAAK,KAAK,MAAM;AAGhB,UAAI,QAAQ,CAAC;AACb,eAAS,IAAI,GAAG,MAAM,IAAI,KAAK,QAAQ,KAAK;AACxC,YAAI,IAAI,KAAK,CAAC;AACd,aAAK,OAAO,CAAC;AACb,eAAO,WAAW,GAAG,IAAI;AACzB,cAAM,KAAK,IAAI;AAAA,MACnB;AAGA,UAAI,UAAU,IAAI,MAAM,EAAE;AAC1B,WAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAK,SAAQ,OAAO,WAAW,MAAM,CAAC,CAAC,CAAC;AAEtE,UAAI,cAAc,YAAY;AAC9B,UAAI,iBAAiB,kBAAkB;AAGvC,aAAO,MAAM,QAAQ;AACjB,YAAI,OAAO,MAAM,MAAM;AACvB,YAAI,IAAI,KAAK;AACb,YAAI,IAAI,KAAK,KAAK;AAGlB,YAAI,QAAQ,UAAU,GAAG,CAAC;AAC1B,YAAI,QAAQ,eAAgB;AAE5B,YAAI,WAAW,QAAQ;AAGvB,YAAI,cAAc,MAAM,KAAK,KAAK,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,GAAG,UAAU,OAAO;AAG9E,YAAI,KAAK,KAAK,IAAI,UAAU,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,UAAU;AAE7D,gBAAM,KAAK,IAAI;AACf,gBAAM,KAAK,WAAW,GAAG,IAAI,CAAC;AAG9B,eAAK,OAAO,CAAC;AACb,kBAAQ,OAAO,IAAI;AACnB,kBAAQ,OAAO,WAAW,IAAI,CAAC;AAC/B,kBAAQ,OAAO,WAAW,KAAK,IAAI,CAAC;AAAA,QACxC;AAAA,MACJ;AAGA,aAAO;AACP,UAAI,UAAU,CAAC;AACf,SAAG;AACC,gBAAQ,KAAK,KAAK,CAAC;AACnB,eAAO,KAAK;AAAA,MAChB,SAAS,SAAS;AAElB,cAAQ,KAAK,KAAK,CAAC;AAEnB,aAAO;AAAA,IACX;AAEA,aAAS,cAAc,MAAM,GAAG,GAAG,GAAG,GAAG,SAAS,SAAS;AACvD,UAAI,QAAQ,IAAI,MAAM,CAAC,GAAG,WAAW;AACrC,UAAI,OAAO,KAAK;AAIhB,aAAO,MAAM;AACT,iBAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,cAAI,QAAQ,KAAK,SAAS,CAAC;AAE3B,cAAI,OAAO,KAAK,OAAO,UAAU,OAAO,GAAG,CAAC,IAAI,aAAa,GAAG,GAAG,KAAK;AACxE,cAAI,OAAO,QAAS;AAEpB,gBAAM,KAAK;AAAA,YACP,MAAM;AAAA,YACN;AAAA,UACJ,CAAC;AAAA,QACL;AAEA,eAAO,MAAM,UAAU,CAAC,MAAM,KAAK,EAAE,KAAK,UAAU;AAChD,cAAI,OAAO,MAAM,IAAI;AACrB,cAAI,IAAI,KAAK;AAIb,cAAI,KAAK,UAAU,GAAG,GAAG,CAAC;AAC1B,cAAI,KAAK,UAAU,GAAG,GAAG,CAAC;AAC1B,cAAI,KAAK,OAAO,MAAM,KAAK,OAAO,MAC9B,gBAAgB,GAAG,GAAG,OAAO,KAC7B,gBAAgB,GAAG,GAAG,OAAO,EAAG,QAAO;AAAA,QAC/C;AAEA,eAAO,MAAM,IAAI;AACjB,YAAI,KAAM,QAAO,KAAK;AAAA,MAC1B;AAEA,aAAO;AAAA,IACX;AAEA,aAAS,YAAY,GAAG,GAAG;AACvB,aAAO,EAAE,OAAO,EAAE;AAAA,IACtB;AAGA,aAAS,aAAa,GAAG,GAAG,MAAM;AAC9B,UAAI,OAAO,GAAG,IAAI,KAAK,OAAO,GAAG,IAAI,EAAG,QAAO;AAC/C,UAAI,KAAK,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACxF,UAAI,OAAO,EAAG,QAAO;AACrB,UAAI,KAAK,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACxF,UAAI,OAAO,EAAG,QAAO;AACrB,UAAI,KAAK,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACxF,UAAI,OAAO,EAAG,QAAO;AACrB,UAAI,KAAK,aAAa,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,MAAM,KAAK,MAAM,KAAK,MAAM,KAAK,IAAI;AACxF,UAAI,OAAO,EAAG,QAAO;AACrB,aAAO,KAAK,IAAI,IAAI,IAAI,IAAI,EAAE;AAAA,IAClC;AAEA,aAAS,OAAO,GAAG,MAAM;AACrB,aAAO,EAAE,CAAC,KAAK,KAAK,QACb,EAAE,CAAC,KAAK,KAAK,QACb,EAAE,CAAC,KAAK,KAAK,QACb,EAAE,CAAC,KAAK,KAAK;AAAA,IACxB;AAGA,aAAS,gBAAgB,GAAG,GAAG,SAAS;AACpC,UAAI,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,UAAI,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,UAAI,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9B,UAAI,OAAO,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAE9B,UAAI,QAAQ,QAAQ,OAAO,EAAC,MAAY,MAAY,MAAY,KAAU,CAAC;AAC3E,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAI,WAAW,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAG,QAAO;AAAA,MAC9D;AACA,aAAO;AAAA,IACX;AAEA,aAAS,MAAM,IAAI,IAAI,IAAI;AACvB,aAAO,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,IAC1D;AAGA,aAAS,WAAW,IAAI,IAAI,IAAI,IAAI;AAChC,aAAO,OAAO,MAAM,OAAO,MACvB,MAAM,IAAI,IAAI,EAAE,IAAI,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI,KAC9C,MAAM,IAAI,IAAI,EAAE,IAAI,MAAM,MAAM,IAAI,IAAI,EAAE,IAAI;AAAA,IACtD;AAGA,aAAS,WAAW,MAAM;AACtB,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK,KAAK;AACnB,WAAK,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,WAAK,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,WAAK,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,WAAK,OAAO,KAAK,IAAI,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACjC,aAAO;AAAA,IACX;AAGA,aAAS,eAAe,QAAQ;AAC5B,UAAI,OAAO,OAAO,CAAC;AACnB,UAAI,MAAM,OAAO,CAAC;AAClB,UAAI,QAAQ,OAAO,CAAC;AACpB,UAAI,SAAS,OAAO,CAAC;AAGrB,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,YAAI,IAAI,OAAO,CAAC;AAChB,YAAI,EAAE,CAAC,IAAI,KAAK,CAAC,EAAG,QAAO;AAC3B,YAAI,EAAE,CAAC,IAAI,MAAM,CAAC,EAAG,SAAQ;AAC7B,YAAI,EAAE,CAAC,IAAI,IAAI,CAAC,EAAG,OAAM;AACzB,YAAI,EAAE,CAAC,IAAI,OAAO,CAAC,EAAG,UAAS;AAAA,MACnC;AAGA,UAAI,OAAO,CAAC,MAAM,KAAK,OAAO,MAAM;AACpC,UAAI,WAAW,KAAK,MAAM;AAC1B,WAAK,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AAChC,YAAI,CAAC,eAAe,OAAO,CAAC,GAAG,IAAI,EAAG,UAAS,KAAK,OAAO,CAAC,CAAC;AAAA,MACjE;AAGA,aAAO,WAAW,QAAQ;AAAA,IAC9B;AAGA,aAAS,WAAW,GAAG,MAAM;AACzB,UAAI,OAAO;AAAA,QACP;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACV;AAEA,UAAI,CAAC,MAAM;AACP,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MAEhB,OAAO;AACH,aAAK,OAAO,KAAK;AACjB,aAAK,OAAO;AACZ,aAAK,KAAK,OAAO;AACjB,aAAK,OAAO;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAGA,aAAS,UAAU,IAAI,IAAI;AAEvB,UAAI,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC,GACjB,KAAK,GAAG,CAAC,IAAI,GAAG,CAAC;AAErB,aAAO,KAAK,KAAK,KAAK;AAAA,IAC1B;AAGA,aAAS,UAAU,GAAG,IAAI,IAAI;AAE1B,UAAI,IAAI,GAAG,CAAC,GACR,IAAI,GAAG,CAAC,GACR,KAAK,GAAG,CAAC,IAAI,GACb,KAAK,GAAG,CAAC,IAAI;AAEjB,UAAI,OAAO,KAAK,OAAO,GAAG;AAEtB,YAAI,MAAM,EAAE,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,IAAI,KAAK,OAAO,KAAK,KAAK,KAAK;AAE9D,YAAI,IAAI,GAAG;AACP,cAAI,GAAG,CAAC;AACR,cAAI,GAAG,CAAC;AAAA,QAEZ,WAAW,IAAI,GAAG;AACd,eAAK,KAAK;AACV,eAAK,KAAK;AAAA,QACd;AAAA,MACJ;AAEA,WAAK,EAAE,CAAC,IAAI;AACZ,WAAK,EAAE,CAAC,IAAI;AAEZ,aAAO,KAAK,KAAK,KAAK;AAAA,IAC1B;AAGA,aAAS,aAAa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAClD,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,KAAK;AACd,UAAI,IAAI,KAAK,KAAK,KAAK;AACvB,UAAI,IAAI,KAAK,KAAK,KAAK;AACvB,UAAI,IAAI,KAAK,KAAK,KAAK;AACvB,UAAI,IAAI,KAAK,KAAK,KAAK;AACvB,UAAI,IAAI,KAAK,KAAK,KAAK;AACvB,UAAI,IAAI,IAAI,IAAI,IAAI;AAEpB,UAAI,IAAI,IAAI,IAAI;AAChB,UAAI,KAAK;AACT,UAAI,KAAK;AAET,UAAI,MAAM,GAAG;AACT,aAAK;AACL,aAAK;AACL,aAAK;AACL,aAAK;AAAA,MACT,OAAO;AACH,aAAK,IAAI,IAAI,IAAI;AACjB,aAAK,IAAI,IAAI,IAAI;AACjB,YAAI,KAAK,GAAG;AACR,eAAK;AACL,eAAK;AACL,eAAK;AAAA,QACT,WAAW,KAAK,IAAI;AAChB,eAAK;AACL,eAAK,IAAI;AACT,eAAK;AAAA,QACT;AAAA,MACJ;AAEA,UAAI,KAAK,GAAK;AACV,aAAK;AACL,YAAI,CAAC,IAAI,EAAK,MAAK;AAAA,iBACV,CAAC,IAAI,EAAG,MAAK;AAAA,aACjB;AACD,eAAK,CAAC;AACN,eAAK;AAAA,QACT;AAAA,MACJ,WAAW,KAAK,IAAI;AAChB,aAAK;AACL,YAAK,CAAC,IAAI,IAAK,EAAK,MAAK;AAAA,iBAChB,CAAC,IAAI,IAAI,EAAG,MAAK;AAAA,aACrB;AACD,eAAK,CAAC,IAAI;AACV,eAAK;AAAA,QACT;AAAA,MACJ;AAEA,WAAK,OAAO,IAAI,IAAI,KAAK;AACzB,WAAK,OAAO,IAAI,IAAI,KAAK;AAEzB,UAAI,MAAM,IAAI,MAAM,KAAK,KAAK;AAC9B,UAAI,MAAM,IAAI,MAAM,KAAK,KAAK;AAC9B,UAAI,OAAO,IAAI,MAAM,KAAK,KAAK;AAC/B,UAAI,OAAO,IAAI,MAAM,KAAK,KAAK;AAC/B,UAAI,KAAK,MAAM;AACf,UAAI,KAAK,MAAM;AAEf,aAAO,KAAK,KAAK,KAAK;AAAA,IAC1B;AAEA,aAAS,WAAW,GAAG,GAAG;AACtB,aAAO,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IACnD;AAEA,aAAS,WAAW,QAAQ;AACxB,aAAO,KAAK,UAAU;AAEtB,UAAI,QAAQ,CAAC;AACb,eAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,eAAO,MAAM,UAAU,KAAK,MAAM,MAAM,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM,SAAS,CAAC,GAAG,OAAO,CAAC,CAAC,KAAK,GAAG;AACjG,gBAAM,IAAI;AAAA,QACd;AACA,cAAM,KAAK,OAAO,CAAC,CAAC;AAAA,MACxB;AAEA,UAAI,QAAQ,CAAC;AACb,eAAS,KAAK,OAAO,SAAS,GAAG,MAAM,GAAG,MAAM;AAC5C,eAAO,MAAM,UAAU,KAAK,MAAM,MAAM,MAAM,SAAS,CAAC,GAAG,MAAM,MAAM,SAAS,CAAC,GAAG,OAAO,EAAE,CAAC,KAAK,GAAG;AAClG,gBAAM,IAAI;AAAA,QACd;AACA,cAAM,KAAK,OAAO,EAAE,CAAC;AAAA,MACzB;AAEA,YAAM,IAAI;AACV,YAAM,IAAI;AACV,aAAO,MAAM,OAAO,KAAK;AAAA,IAC7B;AAAA;AAAA;", "names": ["t", "r", "e", "a", "h", "n", "o", "s", "l", "f", "u", "m", "c", "p", "d", "i", "t", "e", "n", "r", "o", "f", "i", "u", "c", "s", "a", "d", "l", "p", "b", "y", "h", "M", "x", "j", "m", "a", "b"]}