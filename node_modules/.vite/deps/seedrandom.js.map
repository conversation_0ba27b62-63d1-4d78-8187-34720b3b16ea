{"version": 3, "sources": ["../../seedrandom/lib/alea.js", "../../seedrandom/lib/xor128.js", "../../seedrandom/lib/xorwow.js", "../../seedrandom/lib/xorshift7.js", "../../seedrandom/lib/xor4096.js", "../../seedrandom/lib/tychei.js", "browser-external:crypto", "../../seedrandom/seedrandom.js", "../../seedrandom/index.js"], "sourcesContent": ["// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"crypto\" has been externalized for browser compatibility. Cannot access \"crypto.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n"], "mappings": ";;;;;AAAA;AAAA;AA2BA,KAAC,SAAS,QAAQA,SAAQC,SAAQ;AAElC,eAAS,KAAK,MAAM;AAClB,YAAI,KAAK,MAAM,OAAO,KAAK;AAE3B,WAAG,OAAO,WAAW;AACnB,cAAI,IAAI,UAAU,GAAG,KAAK,GAAG,IAAI;AACjC,aAAG,KAAK,GAAG;AACX,aAAG,KAAK,GAAG;AACX,iBAAO,GAAG,KAAK,KAAK,GAAG,IAAI,IAAI;AAAA,QACjC;AAGA,WAAG,IAAI;AACP,WAAG,KAAK,KAAK,GAAG;AAChB,WAAG,KAAK,KAAK,GAAG;AAChB,WAAG,KAAK,KAAK,GAAG;AAChB,WAAG,MAAM,KAAK,IAAI;AAClB,YAAI,GAAG,KAAK,GAAG;AAAE,aAAG,MAAM;AAAA,QAAG;AAC7B,WAAG,MAAM,KAAK,IAAI;AAClB,YAAI,GAAG,KAAK,GAAG;AAAE,aAAG,MAAM;AAAA,QAAG;AAC7B,WAAG,MAAM,KAAK,IAAI;AAClB,YAAI,GAAG,KAAK,GAAG;AAAE,aAAG,MAAM;AAAA,QAAG;AAC7B,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE;AACR,UAAE,KAAK,EAAE;AACT,UAAE,KAAK,EAAE;AACT,UAAE,KAAK,EAAE;AACT,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,KAAK,IAAI,KAAK,IAAI,GAClB,QAAQ,QAAQ,KAAK,OACrB,OAAO,GAAG;AACd,aAAK,QAAQ,WAAW;AAAE,iBAAQ,GAAG,KAAK,IAAI,aAAe;AAAA,QAAG;AAChE,aAAK,SAAS,WAAW;AACvB,iBAAO,KAAK,KAAK,KAAK,IAAI,UAAW,KAAK;AAAA,QAC5C;AACA,aAAK,QAAQ;AACb,YAAI,OAAO;AACT,cAAI,OAAO,SAAU,SAAU,MAAK,OAAO,EAAE;AAC7C,eAAK,QAAQ,WAAW;AAAE,mBAAO,KAAK,IAAI,CAAC,CAAC;AAAA,UAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,eAAS,OAAO;AACd,YAAI,IAAI;AAER,YAAI,OAAO,SAAS,MAAM;AACxB,iBAAO,OAAO,IAAI;AAClB,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,iBAAK,KAAK,WAAW,CAAC;AACtB,gBAAI,IAAI,sBAAsB;AAC9B,gBAAI,MAAM;AACV,iBAAK;AACL,iBAAK;AACL,gBAAI,MAAM;AACV,iBAAK;AACL,iBAAK,IAAI;AAAA,UACX;AACA,kBAAQ,MAAM,KAAK;AAAA,QACrB;AAEA,eAAO;AAAA,MACT;AAGA,UAAID,WAAUA,QAAO,SAAS;AAC5B,QAAAA,QAAO,UAAU;AAAA,MACnB,WAAWC,WAAUA,QAAO,KAAK;AAC/B,QAAAA,QAAO,WAAW;AAAE,iBAAO;AAAA,QAAM,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IAEA;AAAA,MACE;AAAA,MACC,OAAO,UAAW,YAAY;AAAA;AAAA,MAC9B,OAAO,UAAW,cAAc;AAAA;AAAA,IACnC;AAAA;AAAA;;;AC/GA;AAAA;AAGA,KAAC,SAAS,QAAQC,SAAQC,SAAQ;AAElC,eAAS,OAAO,MAAM;AACpB,YAAI,KAAK,MAAM,UAAU;AAEzB,WAAG,IAAI;AACP,WAAG,IAAI;AACP,WAAG,IAAI;AACP,WAAG,IAAI;AAGP,WAAG,OAAO,WAAW;AACnB,cAAI,IAAI,GAAG,IAAK,GAAG,KAAK;AACxB,aAAG,IAAI,GAAG;AACV,aAAG,IAAI,GAAG;AACV,aAAG,IAAI,GAAG;AACV,iBAAO,GAAG,KAAM,GAAG,MAAM,KAAM,IAAK,MAAM;AAAA,QAC5C;AAEA,YAAI,UAAU,OAAO,IAAI;AAEvB,aAAG,IAAI;AAAA,QACT,OAAO;AAEL,qBAAW;AAAA,QACb;AAGA,iBAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,IAAI,KAAK;AAC5C,aAAG,KAAK,QAAQ,WAAW,CAAC,IAAI;AAChC,aAAG,KAAK;AAAA,QACV;AAAA,MACF;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,KAAK,IAAI,OAAO,IAAI,GACpB,QAAQ,QAAQ,KAAK,OACrB,OAAO,WAAW;AAAE,kBAAQ,GAAG,KAAK,MAAM,KAAK;AAAA,QAAa;AAChE,aAAK,SAAS,WAAW;AACvB,aAAG;AACD,gBAAI,MAAM,GAAG,KAAK,MAAM,IACpB,OAAO,GAAG,KAAK,MAAM,KAAK,YAC1B,UAAU,MAAM,QAAQ,KAAK;AAAA,UACnC,SAAS,WAAW;AACpB,iBAAO;AAAA,QACT;AACA,aAAK,QAAQ,GAAG;AAChB,aAAK,QAAQ;AACb,YAAI,OAAO;AACT,cAAI,OAAO,SAAU,SAAU,MAAK,OAAO,EAAE;AAC7C,eAAK,QAAQ,WAAW;AAAE,mBAAO,KAAK,IAAI,CAAC,CAAC;AAAA,UAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,UAAID,WAAUA,QAAO,SAAS;AAC5B,QAAAA,QAAO,UAAU;AAAA,MACnB,WAAWC,WAAUA,QAAO,KAAK;AAC/B,QAAAA,QAAO,WAAW;AAAE,iBAAO;AAAA,QAAM,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IAEA;AAAA,MACE;AAAA,MACC,OAAO,UAAW,YAAY;AAAA;AAAA,MAC9B,OAAO,UAAW,cAAc;AAAA;AAAA,IACnC;AAAA;AAAA;;;AC9EA;AAAA;AAGA,KAAC,SAAS,QAAQC,SAAQC,SAAQ;AAElC,eAAS,OAAO,MAAM;AACpB,YAAI,KAAK,MAAM,UAAU;AAGzB,WAAG,OAAO,WAAW;AACnB,cAAI,IAAK,GAAG,IAAK,GAAG,MAAM;AAC1B,aAAG,IAAI,GAAG;AAAG,aAAG,IAAI,GAAG;AAAG,aAAG,IAAI,GAAG;AAAG,aAAG,IAAI,GAAG;AACjD,kBAAQ,GAAG,IAAK,GAAG,IAAI,SAAS,MAC5B,GAAG,IAAK,GAAG,IAAK,GAAG,KAAK,KAAO,IAAK,KAAK,MAAO;AAAA,QACtD;AAEA,WAAG,IAAI;AACP,WAAG,IAAI;AACP,WAAG,IAAI;AACP,WAAG,IAAI;AACP,WAAG,IAAI;AAEP,YAAI,UAAU,OAAO,IAAI;AAEvB,aAAG,IAAI;AAAA,QACT,OAAO;AAEL,qBAAW;AAAA,QACb;AAGA,iBAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,IAAI,KAAK;AAC5C,aAAG,KAAK,QAAQ,WAAW,CAAC,IAAI;AAChC,cAAI,KAAK,QAAQ,QAAQ;AACvB,eAAG,IAAI,GAAG,KAAK,KAAK,GAAG,MAAM;AAAA,UAC/B;AACA,aAAG,KAAK;AAAA,QACV;AAAA,MACF;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,KAAK,IAAI,OAAO,IAAI,GACpB,QAAQ,QAAQ,KAAK,OACrB,OAAO,WAAW;AAAE,kBAAQ,GAAG,KAAK,MAAM,KAAK;AAAA,QAAa;AAChE,aAAK,SAAS,WAAW;AACvB,aAAG;AACD,gBAAI,MAAM,GAAG,KAAK,MAAM,IACpB,OAAO,GAAG,KAAK,MAAM,KAAK,YAC1B,UAAU,MAAM,QAAQ,KAAK;AAAA,UACnC,SAAS,WAAW;AACpB,iBAAO;AAAA,QACT;AACA,aAAK,QAAQ,GAAG;AAChB,aAAK,QAAQ;AACb,YAAI,OAAO;AACT,cAAI,OAAO,SAAU,SAAU,MAAK,OAAO,EAAE;AAC7C,eAAK,QAAQ,WAAW;AAAE,mBAAO,KAAK,IAAI,CAAC,CAAC;AAAA,UAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,UAAID,WAAUA,QAAO,SAAS;AAC5B,QAAAA,QAAO,UAAU;AAAA,MACnB,WAAWC,WAAUA,QAAO,KAAK;AAC/B,QAAAA,QAAO,WAAW;AAAE,iBAAO;AAAA,QAAM,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IAEA;AAAA,MACE;AAAA,MACC,OAAO,UAAW,YAAY;AAAA;AAAA,MAC9B,OAAO,UAAW,cAAc;AAAA;AAAA,IACnC;AAAA;AAAA;;;ACnFA;AAAA;AAKA,KAAC,SAAS,QAAQC,SAAQC,SAAQ;AAElC,eAAS,OAAO,MAAM;AACpB,YAAI,KAAK;AAGT,WAAG,OAAO,WAAW;AAEnB,cAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG;AAC9B,cAAI,EAAE,CAAC;AAAG,eAAM,MAAM;AAAI,cAAI,IAAK,KAAK;AACxC,cAAI,EAAG,IAAI,IAAK,CAAC;AAAG,eAAK,IAAK,MAAM;AACpC,cAAI,EAAG,IAAI,IAAK,CAAC;AAAG,eAAK,IAAK,MAAM;AACpC,cAAI,EAAG,IAAI,IAAK,CAAC;AAAG,eAAK,IAAK,KAAK;AACnC,cAAI,EAAG,IAAI,IAAK,CAAC;AAAG,cAAI,IAAK,KAAK;AAAK,eAAK,IAAK,KAAK;AACtD,YAAE,CAAC,IAAI;AACP,aAAG,IAAK,IAAI,IAAK;AACjB,iBAAO;AAAA,QACT;AAEA,iBAAS,KAAKC,KAAIC,OAAM;AACtB,cAAI,GAAG,GAAG,IAAI,CAAC;AAEf,cAAIA,WAAUA,QAAO,IAAI;AAEvB,gBAAI,EAAE,CAAC,IAAIA;AAAA,UACb,OAAO;AAEL,YAAAA,QAAO,KAAKA;AACZ,iBAAK,IAAI,GAAG,IAAIA,MAAK,QAAQ,EAAE,GAAG;AAChC,gBAAE,IAAI,CAAC,IAAK,EAAE,IAAI,CAAC,KAAK,KACnBA,MAAK,WAAW,CAAC,IAAI,EAAG,IAAI,IAAK,CAAC,KAAK;AAAA,YAC9C;AAAA,UACF;AAEA,iBAAO,EAAE,SAAS,EAAG,GAAE,KAAK,CAAC;AAC7B,eAAK,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE;AACrC,cAAI,KAAK,EAAG,KAAI,EAAE,CAAC,IAAI;AAAA,cAAS,KAAI,EAAE,CAAC;AAEvC,UAAAD,IAAG,IAAI;AACP,UAAAA,IAAG,IAAI;AAGP,eAAK,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG;AACxB,YAAAA,IAAG,KAAK;AAAA,UACV;AAAA,QACF;AAEA,aAAK,IAAI,IAAI;AAAA,MACf;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE,EAAE,MAAM;AAChB,UAAE,IAAI,EAAE;AACR,eAAO;AAAA,MACT;AAEA,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,QAAQ,KAAM,QAAO,CAAE,oBAAI;AAC/B,YAAI,KAAK,IAAI,OAAO,IAAI,GACpB,QAAQ,QAAQ,KAAK,OACrB,OAAO,WAAW;AAAE,kBAAQ,GAAG,KAAK,MAAM,KAAK;AAAA,QAAa;AAChE,aAAK,SAAS,WAAW;AACvB,aAAG;AACD,gBAAI,MAAM,GAAG,KAAK,MAAM,IACpB,OAAO,GAAG,KAAK,MAAM,KAAK,YAC1B,UAAU,MAAM,QAAQ,KAAK;AAAA,UACnC,SAAS,WAAW;AACpB,iBAAO;AAAA,QACT;AACA,aAAK,QAAQ,GAAG;AAChB,aAAK,QAAQ;AACb,YAAI,OAAO;AACT,cAAI,MAAM,EAAG,MAAK,OAAO,EAAE;AAC3B,eAAK,QAAQ,WAAW;AAAE,mBAAO,KAAK,IAAI,CAAC,CAAC;AAAA,UAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,UAAIF,WAAUA,QAAO,SAAS;AAC5B,QAAAA,QAAO,UAAU;AAAA,MACnB,WAAWC,WAAUA,QAAO,KAAK;AAC/B,QAAAA,QAAO,WAAW;AAAE,iBAAO;AAAA,QAAM,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,YAAY;AAAA,MACnB;AAAA,IAEA;AAAA,MACE;AAAA,MACC,OAAO,UAAW,YAAY;AAAA;AAAA,MAC9B,OAAO,UAAW,cAAc;AAAA;AAAA,IACnC;AAAA;AAAA;;;AC/FA;AAAA;AAyBA,KAAC,SAAS,QAAQG,SAAQC,SAAQ;AAElC,eAAS,OAAO,MAAM;AACpB,YAAI,KAAK;AAGT,WAAG,OAAO,WAAW;AACnB,cAAI,IAAI,GAAG,GACP,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG;AAE3B,aAAG,IAAI,IAAK,IAAI,aAAc;AAE9B,cAAI,EAAG,IAAI,KAAM,GAAG;AACpB,cAAI,EAAE,IAAM,IAAI,IAAK,GAAI;AACzB,eAAK,KAAK;AACV,eAAK,KAAK;AACV,eAAK,MAAM;AACX,eAAK,MAAM;AAEX,cAAI,EAAE,CAAC,IAAI,IAAI;AACf,aAAG,IAAI;AAEP,iBAAQ,KAAK,IAAK,MAAM,MAAQ;AAAA,QAClC;AAEA,iBAAS,KAAKC,KAAIC,OAAM;AACtB,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,QAAQ;AACnC,cAAIA,WAAUA,QAAO,IAAI;AAEvB,gBAAIA;AACJ,YAAAA,QAAO;AAAA,UACT,OAAO;AAEL,YAAAA,QAAOA,QAAO;AACd,gBAAI;AACJ,oBAAQ,KAAK,IAAI,OAAOA,MAAK,MAAM;AAAA,UACrC;AAEA,eAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,EAAE,GAAG;AAEnC,gBAAIA,MAAM,MAAKA,MAAK,YAAY,IAAI,MAAMA,MAAK,MAAM;AAErD,gBAAI,MAAM,EAAG,KAAI;AACjB,iBAAK,KAAK;AACV,iBAAK,MAAM;AACX,iBAAK,KAAK;AACV,iBAAK,MAAM;AACX,gBAAI,KAAK,GAAG;AACV,kBAAK,IAAI,aAAc;AACvB,kBAAK,EAAE,IAAI,GAAG,KAAM,IAAI;AACxB,kBAAK,KAAK,IAAK,IAAI,IAAI;AAAA,YACzB;AAAA,UACF;AAEA,cAAI,KAAK,KAAK;AACZ,eAAGA,SAAQA,MAAK,UAAU,KAAK,GAAG,IAAI;AAAA,UACxC;AAIA,cAAI;AACJ,eAAK,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG;AAC5B,gBAAI,EAAG,IAAI,KAAM,GAAG;AACpB,gBAAI,EAAE,IAAM,IAAI,IAAK,GAAI;AACzB,iBAAK,KAAK;AACV,iBAAK,KAAK;AACV,iBAAK,MAAM;AACX,iBAAK,MAAM;AACX,cAAE,CAAC,IAAI,IAAI;AAAA,UACb;AAEA,UAAAD,IAAG,IAAI;AACP,UAAAA,IAAG,IAAI;AACP,UAAAA,IAAG,IAAI;AAAA,QACT;AAEA,aAAK,IAAI,IAAI;AAAA,MACf;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE,EAAE,MAAM;AAChB,eAAO;AAAA,MACT;AAAC;AAED,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,QAAQ,KAAM,QAAO,CAAE,oBAAI;AAC/B,YAAI,KAAK,IAAI,OAAO,IAAI,GACpB,QAAQ,QAAQ,KAAK,OACrB,OAAO,WAAW;AAAE,kBAAQ,GAAG,KAAK,MAAM,KAAK;AAAA,QAAa;AAChE,aAAK,SAAS,WAAW;AACvB,aAAG;AACD,gBAAI,MAAM,GAAG,KAAK,MAAM,IACpB,OAAO,GAAG,KAAK,MAAM,KAAK,YAC1B,UAAU,MAAM,QAAQ,KAAK;AAAA,UACnC,SAAS,WAAW;AACpB,iBAAO;AAAA,QACT;AACA,aAAK,QAAQ,GAAG;AAChB,aAAK,QAAQ;AACb,YAAI,OAAO;AACT,cAAI,MAAM,EAAG,MAAK,OAAO,EAAE;AAC3B,eAAK,QAAQ,WAAW;AAAE,mBAAO,KAAK,IAAI,CAAC,CAAC;AAAA,UAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,UAAIF,WAAUA,QAAO,SAAS;AAC5B,QAAAA,QAAO,UAAU;AAAA,MACnB,WAAWC,WAAUA,QAAO,KAAK;AAC/B,QAAAA,QAAO,WAAW;AAAE,iBAAO;AAAA,QAAM,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IAEA;AAAA,MACE;AAAA;AAAA,MACC,OAAO,UAAW,YAAY;AAAA;AAAA,MAC9B,OAAO,UAAW,cAAc;AAAA;AAAA,IACnC;AAAA;AAAA;;;ACjJA;AAAA;AAIA,KAAC,SAAS,QAAQG,SAAQC,SAAQ;AAElC,eAAS,OAAO,MAAM;AACpB,YAAI,KAAK,MAAM,UAAU;AAGzB,WAAG,OAAO,WAAW;AACnB,cAAI,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG;AACzC,cAAK,KAAK,KAAO,MAAM,IAAK;AAC5B,cAAK,IAAI,IAAK;AACd,cAAK,KAAK,KAAO,MAAM,IAAK;AAC5B,cAAK,IAAI,IAAK;AACd,aAAG,IAAI,IAAK,KAAK,KAAO,MAAM,KAAM;AACpC,aAAG,IAAI,IAAK,IAAI,IAAK;AACrB,aAAG,IAAK,KAAK,KAAO,MAAM,KAAM;AAChC,iBAAO,GAAG,IAAK,IAAI,IAAK;AAAA,QAC1B;AAkBA,WAAG,IAAI;AACP,WAAG,IAAI;AACP,WAAG,IAAI,aAAa;AACpB,WAAG,IAAI;AAEP,YAAI,SAAS,KAAK,MAAM,IAAI,GAAG;AAE7B,aAAG,IAAK,OAAO,aAAe;AAC9B,aAAG,IAAI,OAAO;AAAA,QAChB,OAAO;AAEL,qBAAW;AAAA,QACb;AAGA,iBAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,IAAI,KAAK;AAC5C,aAAG,KAAK,QAAQ,WAAW,CAAC,IAAI;AAChC,aAAG,KAAK;AAAA,QACV;AAAA,MACF;AAEA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,eAAO;AAAA,MACT;AAAC;AAED,eAAS,KAAK,MAAM,MAAM;AACxB,YAAI,KAAK,IAAI,OAAO,IAAI,GACpB,QAAQ,QAAQ,KAAK,OACrB,OAAO,WAAW;AAAE,kBAAQ,GAAG,KAAK,MAAM,KAAK;AAAA,QAAa;AAChE,aAAK,SAAS,WAAW;AACvB,aAAG;AACD,gBAAI,MAAM,GAAG,KAAK,MAAM,IACpB,OAAO,GAAG,KAAK,MAAM,KAAK,YAC1B,UAAU,MAAM,QAAQ,KAAK;AAAA,UACnC,SAAS,WAAW;AACpB,iBAAO;AAAA,QACT;AACA,aAAK,QAAQ,GAAG;AAChB,aAAK,QAAQ;AACb,YAAI,OAAO;AACT,cAAI,OAAO,SAAU,SAAU,MAAK,OAAO,EAAE;AAC7C,eAAK,QAAQ,WAAW;AAAE,mBAAO,KAAK,IAAI,CAAC,CAAC;AAAA,UAAG;AAAA,QACjD;AACA,eAAO;AAAA,MACT;AAEA,UAAID,WAAUA,QAAO,SAAS;AAC5B,QAAAA,QAAO,UAAU;AAAA,MACnB,WAAWC,WAAUA,QAAO,KAAK;AAC/B,QAAAA,QAAO,WAAW;AAAE,iBAAO;AAAA,QAAM,CAAC;AAAA,MACpC,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IAEA;AAAA,MACE;AAAA,MACC,OAAO,UAAW,YAAY;AAAA;AAAA,MAC9B,OAAO,UAAW,cAAc;AAAA;AAAA,IACnC;AAAA;AAAA;;;ACpGA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,0FAA0F,GAAG,mIAAmI;AAAA,QAC/O;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAwBA,KAAC,SAAU,QAAQ,MAAM,MAAM;AAK/B,UAAI,QAAQ,KACR,SAAS,GACT,SAAS,IACT,UAAU,UACV,aAAa,KAAK,IAAI,OAAO,MAAM,GACnC,eAAe,KAAK,IAAI,GAAG,MAAM,GACjC,WAAW,eAAe,GAC1B,OAAO,QAAQ,GACf;AAMJ,eAAS,WAAW,MAAM,SAAS,UAAU;AAC3C,YAAI,MAAM,CAAC;AACX,kBAAW,WAAW,OAAQ,EAAE,SAAS,KAAK,IAAK,WAAW,CAAC;AAG/D,YAAI,YAAY,OAAO;AAAA,UACrB,QAAQ,UAAU,CAAC,MAAM,SAAS,IAAI,CAAC,IACtC,QAAQ,OAAQ,SAAS,IAAI;AAAA,UAAM;AAAA,QAAC,GAAG,GAAG;AAG7C,YAAI,OAAO,IAAI,KAAK,GAAG;AAIvB,YAAI,OAAO,WAAW;AACpB,cAAI,IAAI,KAAK,EAAE,MAAM,GACjB,IAAI,YACJ,IAAI;AACR,iBAAO,IAAI,cAAc;AACvB,iBAAK,IAAI,KAAK;AACd,iBAAK;AACL,gBAAI,KAAK,EAAE,CAAC;AAAA,UACd;AACA,iBAAO,KAAK,UAAU;AACpB,iBAAK;AACL,iBAAK;AACL,mBAAO;AAAA,UACT;AACA,kBAAQ,IAAI,KAAK;AAAA,QACnB;AAEA,aAAK,QAAQ,WAAW;AAAE,iBAAO,KAAK,EAAE,CAAC,IAAI;AAAA,QAAG;AAChD,aAAK,QAAQ,WAAW;AAAE,iBAAO,KAAK,EAAE,CAAC,IAAI;AAAA,QAAa;AAC1D,aAAK,SAAS;AAGd,eAAO,SAAS,KAAK,CAAC,GAAG,IAAI;AAG7B,gBAAQ,QAAQ,QAAQ,YACpB,SAASC,OAAMC,OAAM,cAAc,OAAO;AACxC,cAAI,OAAO;AAET,gBAAI,MAAM,GAAG;AAAE,mBAAK,OAAO,IAAI;AAAA,YAAG;AAElC,YAAAD,MAAK,QAAQ,WAAW;AAAE,qBAAO,KAAK,MAAM,CAAC,CAAC;AAAA,YAAG;AAAA,UACnD;AAIA,cAAI,cAAc;AAAE,iBAAK,OAAO,IAAIA;AAAM,mBAAOC;AAAA,UAAM,MAIlD,QAAOD;AAAA,QACd;AAAA,UACJ;AAAA,UACA;AAAA,UACA,YAAY,UAAU,QAAQ,SAAU,QAAQ;AAAA,UAChD,QAAQ;AAAA,QAAK;AAAA,MACf;AAYA,eAAS,KAAK,KAAK;AACjB,YAAI,GAAG,SAAS,IAAI,QAChB,KAAK,MAAM,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAGvD,YAAI,CAAC,QAAQ;AAAE,gBAAM,CAAC,QAAQ;AAAA,QAAG;AAGjC,eAAO,IAAI,OAAO;AAChB,YAAE,CAAC,IAAI;AAAA,QACT;AACA,aAAK,IAAI,GAAG,IAAI,OAAO,KAAK;AAC1B,YAAE,CAAC,IAAI,EAAE,IAAI,OAAQ,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC,EAAG;AACtD,YAAE,CAAC,IAAI;AAAA,QACT;AAGA,SAAC,GAAG,IAAI,SAAS,OAAO;AAEtB,cAAIE,IAAG,IAAI,GACPC,KAAI,GAAG,GAAGC,KAAI,GAAG,GAAGC,KAAI,GAAG;AAC/B,iBAAO,SAAS;AACd,YAAAH,KAAIG,GAAEF,KAAI,OAAQA,KAAI,CAAE;AACxB,gBAAI,IAAI,QAAQE,GAAE,QAASA,GAAEF,EAAC,IAAIE,GAAED,KAAI,OAAQA,KAAIF,EAAE,MAAMG,GAAED,EAAC,IAAIF,GAAG;AAAA,UACxE;AACA,aAAG,IAAIC;AAAG,aAAG,IAAIC;AACjB,iBAAO;AAAA,QAIT,GAAG,KAAK;AAAA,MACV;AAMA,eAAS,KAAK,GAAG,GAAG;AAClB,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE;AACR,UAAE,IAAI,EAAE,EAAE,MAAM;AAChB,eAAO;AAAA,MACT;AAAC;AAMD,eAAS,QAAQ,KAAK,OAAO;AAC3B,YAAI,SAAS,CAAC,GAAG,MAAO,OAAO,KAAM;AACrC,YAAI,SAAS,OAAO,UAAU;AAC5B,eAAK,QAAQ,KAAK;AAChB,gBAAI;AAAE,qBAAO,KAAK,QAAQ,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC;AAAA,YAAG,SAAS,GAAG;AAAA,YAAC;AAAA,UACjE;AAAA,QACF;AACA,eAAQ,OAAO,SAAS,SAAS,OAAO,WAAW,MAAM,MAAM;AAAA,MACjE;AAOA,eAAS,OAAO,MAAM,KAAK;AACzB,YAAI,aAAa,OAAO,IAAI,OAAO,IAAI;AACvC,eAAO,IAAI,WAAW,QAAQ;AAC5B,cAAI,OAAO,CAAC,IACV,QAAS,SAAS,IAAI,OAAO,CAAC,IAAI,MAAM,WAAW,WAAW,GAAG;AAAA,QACrE;AACA,eAAO,SAAS,GAAG;AAAA,MACrB;AAOA,eAAS,WAAW;AAClB,YAAI;AACF,cAAI;AACJ,cAAI,eAAe,MAAM,WAAW,cAAc;AAEhD,kBAAM,IAAI,KAAK;AAAA,UACjB,OAAO;AACL,kBAAM,IAAI,WAAW,KAAK;AAC1B,aAAC,OAAO,UAAU,OAAO,UAAU,gBAAgB,GAAG;AAAA,UACxD;AACA,iBAAO,SAAS,GAAG;AAAA,QACrB,SAAS,GAAG;AACV,cAAI,UAAU,OAAO,WACjB,UAAU,WAAW,QAAQ;AACjC,iBAAO,CAAC,CAAC,oBAAI,QAAM,QAAQ,SAAS,OAAO,QAAQ,SAAS,IAAI,CAAC;AAAA,QACnE;AAAA,MACF;AAMA,eAAS,SAAS,GAAG;AACnB,eAAO,OAAO,aAAa,MAAM,GAAG,CAAC;AAAA,MACvC;AASA,aAAO,KAAK,OAAO,GAAG,IAAI;AAM1B,UAAK,OAAO,UAAW,YAAY,OAAO,SAAS;AACjD,eAAO,UAAU;AAEjB,YAAI;AACF,uBAAa;AAAA,QACf,SAAS,IAAI;AAAA,QAAC;AAAA,MAChB,WAAY,OAAO,UAAW,cAAc,OAAO,KAAK;AACtD,eAAO,WAAW;AAAE,iBAAO;AAAA,QAAY,CAAC;AAAA,MAC1C,OAAO;AAEL,aAAK,SAAS,OAAO,IAAI;AAAA,MAC3B;AAAA,IAIA;AAAA;AAAA;AAAA,MAGG,OAAO,SAAS,cAAe,OAAO;AAAA,MACvC,CAAC;AAAA;AAAA,MACD;AAAA;AAAA,IACF;AAAA;AAAA;;;AC5PA,IAAAE,sBAAA;AAAA;AAYA,QAAI,OAAO;AAKX,QAAI,SAAS;AAKb,QAAI,SAAS;AAQb,QAAI,YAAY;AAShB,QAAI,UAAU;AAOd,QAAI,SAAS;AAIb,QAAI,KAAK;AAET,OAAG,OAAO;AACV,OAAG,SAAS;AACZ,OAAG,SAAS;AACZ,OAAG,YAAY;AACf,OAAG,UAAU;AACb,OAAG,SAAS;AAEZ,WAAO,UAAU;AAAA;AAAA;", "names": ["module", "define", "module", "define", "module", "define", "module", "define", "me", "seed", "module", "define", "me", "seed", "module", "define", "prng", "seed", "t", "i", "j", "s", "require_seedrandom"]}