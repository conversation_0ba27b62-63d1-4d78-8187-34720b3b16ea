{"name": "@types/concaveman", "version": "1.1.6", "description": "TypeScript definitions for concaveman", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/concaveman", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/DenisCarriere"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/concaveman"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "c09aa9d5f8b64c6af7f5c85b6c654e6921fcf4443686b64acee35a3176a746ac", "typeScriptVersion": "4.5"}