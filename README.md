# Rice-Scatter Fantasy Map Generator

A browser-based fantasy map generator that emulates the classic "throw rice on paper, trace coast" technique used by fantasy authors and game masters.

## Features

- **Procedural Generation**: Generate unique island/continent shapes using random point clouds and concave hull algorithms
- **Seedable RNG**: Reproducible maps using numeric seeds
- **Real-time Controls**: Adjust parameters and see results instantly
- **Interactive Canvas**: Pan and zoom to explore your generated maps
- **Export Options**: Save maps as PNG or SVG files
- **Responsive Design**: Works on desktop and mobile devices

## Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open your browser** and navigate to the local development URL (typically `http://localhost:5173`)

4. **Generate maps**:
   - Use the sliders to adjust generation parameters
   - Click "🎲 Reroll" for a new random map
   - Use mouse/touch to pan and zoom the canvas
   - Export your favorite maps as PNG or SVG

## Controls

### Parameters

- **Seed**: Numeric seed for reproducible generation (1-999999)
- **Points**: Number of random points to generate (50-1000)
- **Concavity**: Controls how detailed the coastline is (0.1-5.0)
  - Lower values = more detailed, jagged coastlines
  - Higher values = smoother, more convex shapes
- **Smoothness**: Number of smoothing iterations (0-3)
  - 0 = No smoothing (angular coastlines)
  - 3 = Maximum smoothing (very curved coastlines)

### Interactions

- **Mouse/Touch Drag**: Pan around the map
- **Mouse Wheel/Pinch**: Zoom in and out
- **Keyboard Shortcuts**:
  - `R` - Reroll (generate new map)
  - `P` - Export PNG
  - `S` - Export SVG

## Example Seeds

Try these seeds for interesting map shapes:

- **12345** - Classic island chain
- **54321** - Large continent with bays
- **99999** - Archipelago formation
- **42424** - Elongated landmass
- **77777** - Compact circular island

## Technical Details

### Core Algorithm

1. **Point Generation**: Random points distributed in a circular area using polar coordinates
2. **Concave Hull**: Uses the `concaveman` library to create alpha shapes around the point cloud
3. **Smoothing**: Applies Chaikin's corner cutting algorithm for organic curves
4. **Jittering**: Adds Perlin-like noise for natural coastline variation

### Tech Stack

- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first styling
- **HTML5 Canvas** - High-performance 2D rendering
- **concaveman** - Concave hull generation
- **seedrandom** - Deterministic random number generation

### Project Structure

```
src/
├── main.ts          # Application entry point
├── mapGenerator.ts  # Core map generation logic
├── renderer.ts      # Canvas rendering and interactions
├── controls.ts      # UI controls and event handling
├── utils.ts         # Utility functions and algorithms
└── style.css        # Tailwind imports and custom styles
```

## Building for Production

```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

## Development

### Adding New Features

The codebase is modular and extensible:

- **Map Generation**: Modify `mapGenerator.ts` to add new algorithms
- **Rendering**: Extend `renderer.ts` for new visual styles
- **Controls**: Add new parameters in `controls.ts`
- **Algorithms**: Implement new smoothing/generation techniques in `utils.ts`

### Performance Notes

- The app targets 60fps rendering using `requestAnimationFrame`
- Point generation is optimized for counts up to 1000 points
- Canvas uses device pixel ratio for crisp rendering on high-DPI displays

## License

MIT License - feel free to use this code for your own projects!

## Contributing

Contributions welcome! Some ideas for enhancements:

- [ ] Heightmap generation
- [ ] River/lake placement
- [ ] Biome coloring
- [ ] Multiple landmass support
- [ ] Advanced export options (GeoJSON, etc.)
- [ ] Preset parameter combinations
- [ ] Animation/morphing between seeds
